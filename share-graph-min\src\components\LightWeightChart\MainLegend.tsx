import { useEffect, useState } from 'react';
import { useChartContext } from './context';
import { OHLCVExtraData } from '../../lightweight-chart/interface';
import {AdvanceChart} from '../../lightweight-chart/advance-chart';

const calcPercentChange = (point: OHLCVExtraData, prePoint: OHLCVExtraData, chart: AdvanceChart) => {
  const change = point.close - prePoint.close;
  const changePercent = (change / prePoint.close) * 100;
  
  return {
    ...point,
    change,
    changePercent,
    color: change > 0 ? chart.options.upColor : chart.options.downColor,
  };
};

type IMainLegendData = OHLCVExtraData & {
  change: number;
  changePercent: number;
  color: string;
};

const MainLegend = () => {
  const { getChart } = useChartContext();
  const [chartHoverData, chartHoverDataSet] = useState<
    IMainLegendData | undefined
  >();
  const [chartHovered, setChartHovered] = useState<boolean>(false);
  const [lastPoint, lastPointSet] = useState<IMainLegendData | undefined>();


  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    const handle = (point: OHLCVExtraData, prePoint: OHLCVExtraData) => {
      chartHoverDataSet(calcPercentChange(point, prePoint, chart));
    };

    chart.crosshairMoved().subscribe(handle);
    chart.chartHovered().subscribe(setChartHovered);
    setChartHovered(chart.chartHovered().lastParams()?.[0]);

    const chartUpdate = () => {
      const [current, pre] = chart.lastPoint();

      if (current && pre) {
        lastPointSet(calcPercentChange(current, pre, chart));
      }
    };
    chart.updated().subscribe(chartUpdate);
    return () => {
      chart.crosshairMoved().unsubscribe(handle);
      chart.chartHovered().unsubscribe(setChartHovered);
      chart.updated().unsubscribe(chartUpdate);
    };
  }, [getChart]);

  const data = chartHovered ? chartHoverData : lastPoint;
  const chart = getChart();

  if(!chart) return null;
  
  return data ? (
    <div className='main-legend'>
      <span>
        O <span style={{ color: data.color }}>{chart.numberFormatter.decimal(data.open)}</span> H{' '}
        <span style={{ color: data.color }}>{chart.numberFormatter.decimal(data.high)}</span> L{' '}
        <span style={{ color: data.color }}>{chart.numberFormatter.decimal(data.low)}</span> C{' '}
        <span style={{ color: data.color }}>{chart.numberFormatter.decimal(data.close)}</span>{' '}
        <span style={{ color: data.color }}>
          {chart.numberFormatter.decimal(data.change)} ({chart.numberFormatter.percent(data.changePercent / 100)})
        </span>
      </span>
    </div>
  ) : null;
};

export default MainLegend;
