import {Coordinate, DeepPartial, IPriceLine, ISeriesPrimitive, LineStyle, Time} from "lightweight-charts";
import {PrimitivePaneViewBase, SeriesPrimitiveBase} from "../primitive-base";
import {LinePrimitivePaneView, defaultOptions as lineDefaultOptions} from "../pane-view/line";
import {cloneDeep, merge} from "es-toolkit";
import {BitmapCoordinatesRenderingScope} from "fancy-canvas";
import {ensureNotNull} from "../../helpers/assertions";

export interface PrimitivePaneViewBaseOptions {
  backgroundColor: string,
  upPrice: number,
  lowPrice: number,
}

export interface RegionPrimitiveOptions extends PrimitivePaneViewBaseOptions {
  lineWidth: number, 
  lineColor: string;
}

export const defaultOptions: RegionPrimitiveOptions = {
  backgroundColor: '#2196f31a',
  ...lineDefaultOptions,
  upPrice: 0,
  lowPrice: 0,
}

export interface RegionPaneViewData {
  top: Coordinate, 
  bottom: Coordinate
}
export class RegionPaneView extends PrimitivePaneViewBase<PrimitivePaneViewBaseOptions> {
  _drawBackgroundImpl(renderingScope: BitmapCoordinatesRenderingScope): void {
    const ctx = renderingScope.context;
    ctx.scale(renderingScope.horizontalPixelRatio, renderingScope.verticalPixelRatio)
    const width = ctx.canvas.width
    const region = new Path2D;
    const upCoor = ensureNotNull(this.priceToCoordinate(this.options.upPrice))
    const lowCoor = ensureNotNull(this.priceToCoordinate(this.options.lowPrice))
    region.moveTo(0, upCoor)
    region.lineTo(width, upCoor)
    region.lineTo(width, lowCoor)
    region.lineTo(0, lowCoor)
    region.lineTo(0, upCoor)
    region.closePath()
    ctx.beginPath();
    ctx.fillStyle = this.options.backgroundColor
    ctx.fill(region)
  }

  defaultOptions(): PrimitivePaneViewBaseOptions {
    return { 
      backgroundColor: '#2196f31a',
      upPrice: 0,
      lowPrice: 0
    }
  }
}

export class RegionPrimitive extends SeriesPrimitiveBase implements ISeriesPrimitive<Time> {
  bandPaneView: RegionPaneView
  upLinePaneView: LinePrimitivePaneView
  lowLinePaneView: LinePrimitivePaneView
  _options: RegionPrimitiveOptions

  upPriceLine: IPriceLine | null = null
  lowPriceLine: IPriceLine | null = null

  constructor(options: DeepPartial<RegionPrimitiveOptions>) {
    super();
    this._options = merge(cloneDeep(defaultOptions), options)

    this.bandPaneView = new RegionPaneView(this._options)
    this.upLinePaneView = new LinePrimitivePaneView({...this._options, lineDash: LineStyle.LargeDashed})
    this.lowLinePaneView = new LinePrimitivePaneView({...this._options, lineDash: LineStyle.LargeDashed})
    this._paneViews = [this.upLinePaneView, this.bandPaneView, this.lowLinePaneView]
  }

  _updateAllViews(): void {
    const width = this.chart.timeScale().width();
    const { upPrice, lowPrice } = this._options;

    this.upLinePaneView.update([
      {
        x: 0 as Coordinate,
        price: upPrice,
      },
      {
        x: width as Coordinate,
        price: upPrice,
      }
    ])

    this.lowLinePaneView.update([
      {
        x: 0 as Coordinate,
        price: lowPrice,
      },
      {
        x: width as Coordinate,
        price: lowPrice,
      }
    ])
  }
}