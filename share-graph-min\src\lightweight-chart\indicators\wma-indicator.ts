import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from "lightweight-charts";
import { SMAIndicatorOptions } from "./sma-indicator";
import { Context, IIndicatorBar } from "../helpers/execution-indicator";
import { SeriesPrimitiveBase } from "../custom-primitive/primitive-base";
import { LineData, LinePrimitivePaneView } from "../custom-primitive/pane-view/line";
import { ChartIndicator } from "./abstract-indicator";

export interface WMAIndicatorOptions extends SMAIndicatorOptions {}

export const defaultOptions: WMAIndicatorOptions = {
    color: "#03fc03",
    period: 9,
    overlay: true
}

export class WMAPrimitive extends SeriesPrimitiveBase<
SingleValueData | WhitespaceData
> {
    linePrimitive: LinePrimitivePaneView;
    constructor(protected source: WMAIndicator) {
        super();
        this.linePrimitive = new LinePrimitivePaneView({
            lineColor: this.source.options.color,
        });
        this._paneViews = [this.linePrimitive];
    }

    update(indicatorBars: IIndicatorBar<WMAData>[]) {
        const lineData: LineData[] = []
        for(const bar of indicatorBars) {
            const value = bar.value
            if(value) lineData.push({time: bar.time as Time, price: value[0]})
        }

        this.linePrimitive.update(lineData);
    }
}

export type WMAData = readonly [Nominal<number, 'WMA'>]

export default class WMAIndicator extends ChartIndicator<WMAIndicatorOptions, WMAData> {
    wmaPrimitive = new WMAPrimitive(this)

    getDefaultOptions(): WMAIndicatorOptions {
        return defaultOptions
    }

    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {
        series.attachPrimitive(this.wmaPrimitive)
    }

    remove(): void {
        super.remove();
        this.mainSeries?.detachPrimitive(this.wmaPrimitive)
    }

    applyIndicatorData(): void {
        this.wmaPrimitive.update(
            this._executionContext.data
        )
    }

    formula(c: Context) {
        const prices = c.new_var(c.symbol.close, this.options.period);
        if (!prices.calculable()) return;
      
        const values = prices.getAll();
        const period = this.options.period;
      
        if (values.length < period) return;
      
        const weights = Array.from({ length: period }, (_, i) => period - i);
        const weightSum = weights.reduce((a, b) => a + b, 0);
      
        const recent = values.slice(-period);
        const weightedSum = recent.reduce((sum, val, i) => sum + val * weights[i], 0);
      
        const wma = weightedSum / weightSum;
      
        return [wma as Nominal<number, 'WMA'>] as WMAData;
    }

}