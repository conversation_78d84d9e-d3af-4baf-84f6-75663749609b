export interface ILogger {
  debug(name: string, ...args: unknown[]): void;
  info(name: string, ...args: unknown[]): void;
  warn(name: string, ...args: unknown[]): void;
  error(name: string, ...args: unknown[]): void;
}

export enum Log {
  NONE = 0,
  ERROR = 1,
  WARN = 2,
  INFO = 3,
  DEBUG = 4
}

// Module state
let currentLevel = Log.NONE;
let currentLogger: ILogger = {
  debug: (name, ...args) => console.debug(`[${name}]`, ...args),
  info: (name, ...args) => console.info(`[${name}]`, ...args),
  warn: (name, ...args) => console.warn(`[${name}]`, ...args),
  error: (name, ...args) => console.error(`[${name}]`, ...args)
};

export const LogManager = {
  reset: () => { currentLevel = Log.INFO; },
  setLevel: (value: Log) => { currentLevel = value; },
  setLogger: (value: ILogger) => { currentLogger = value; }
};

export class Logger {
  constructor(private name: string, private method?: string) {}

  debug(...args: unknown[]): void {
    if (currentLevel >= Log.DEBUG) currentLogger.debug(this.format(), ...args);
  }

  info(...args: unknown[]): void {
    if (currentLevel >= Log.INFO) currentLogger.info(this.format(), ...args);
  }

  warn(...args: unknown[]): void {
    if (currentLevel >= Log.WARN) currentLogger.warn(this.format(), ...args);
  }

  error(...args: unknown[]): void {
    if (currentLevel >= Log.ERROR) currentLogger.error(this.format(), ...args);
  }

  private format(): string {
    return this.method ? `${this.name}.${this.method}` : this.name;
  }
}

export const log = new Logger('Chart');
