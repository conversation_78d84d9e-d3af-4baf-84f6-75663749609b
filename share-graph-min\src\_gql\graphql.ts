import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** The `Byte` scalar type represents non-fractional whole numeric values. Byte can represent values between 0 and 255. */
  Byte: { input: number; output: number; }
  /** The `Date` scalar represents an ISO-8601 compliant date type. */
  Date: { input: string; output: string; }
  /** The `DateTime` scalar represents an ISO-8601 compliant date time type. */
  DateTime: { input: string; output: string; }
  /** The built-in `Decimal` scalar type. */
  Decimal: { input: number; output: number; }
  /** The `Long` scalar type represents non-fractional signed whole 64-bit numeric values. Long can represent values between -(2^63) and 2^63 - 1. */
  Long: { input: number; output: number; }
  /** The `Short` scalar type represents non-fractional signed whole 16-bit numeric values. Short can represent values between -(2^15) and 2^15 - 1. */
  Short: { input: number; output: number; }
};

export type AnalystEstimates = {
  __typename?: 'AnalystEstimates';
  calendarYear?: Maybe<Scalars['Int']['output']>;
  cumulativeType?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  estimatedEbitAvg?: Maybe<Scalars['Float']['output']>;
  estimatedEbitHigh?: Maybe<Scalars['Float']['output']>;
  estimatedEbitLow?: Maybe<Scalars['Float']['output']>;
  estimatedEbitdaAvg?: Maybe<Scalars['Float']['output']>;
  estimatedEbitdaHigh?: Maybe<Scalars['Float']['output']>;
  estimatedEbitdaLow?: Maybe<Scalars['Float']['output']>;
  estimatedEpsAvg?: Maybe<Scalars['Float']['output']>;
  estimatedEpsHigh?: Maybe<Scalars['Float']['output']>;
  estimatedEpsLow?: Maybe<Scalars['Float']['output']>;
  estimatedNetIncomeAvg?: Maybe<Scalars['Float']['output']>;
  estimatedNetIncomeHigh?: Maybe<Scalars['Float']['output']>;
  estimatedNetIncomeLow?: Maybe<Scalars['Float']['output']>;
  estimatedNumberAnalystRevenue?: Maybe<Scalars['Float']['output']>;
  estimatedNumberAnalystsEps?: Maybe<Scalars['Float']['output']>;
  estimatedRevenueAvg?: Maybe<Scalars['Float']['output']>;
  estimatedRevenueHigh?: Maybe<Scalars['Float']['output']>;
  estimatedRevenueLow?: Maybe<Scalars['Float']['output']>;
  estimatedSgaExpenseAvg?: Maybe<Scalars['Float']['output']>;
  estimatedSgaExpenseHigh?: Maybe<Scalars['Float']['output']>;
  estimatedSgaExpenseLow?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['String']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
};

/** A segment of a collection. */
export type AnalystEstimatesCollectionSegment = {
  __typename?: 'AnalystEstimatesCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<AnalystEstimates>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type AnalystRecommendationDto = {
  __typename?: 'AnalystRecommendationDto';
  analystRatingBuy: Scalars['Int']['output'];
  analystRatingHold: Scalars['Int']['output'];
  analystRatingSell: Scalars['Int']['output'];
  analystRatingStrongBuy: Scalars['Int']['output'];
  analystRatingStrongSell: Scalars['Int']['output'];
  date: Scalars['DateTime']['output'];
};

export type AnnualDividend = {
  __typename?: 'AnnualDividend';
  /** Original dividend currency */
  currency?: Maybe<Scalars['String']['output']>;
  /** Exchange currency (if provided) that price is converted from original dividend currency into this */
  exchangeCurrency?: Maybe<Scalars['String']['output']>;
  instrument: Instrument;
  instrumentId: Scalars['Int']['output'];
  total?: Maybe<Scalars['Decimal']['output']>;
  year?: Maybe<Scalars['Int']['output']>;
  yield?: Maybe<Scalars['Decimal']['output']>;
};

export type AnnualDividendDtoFilterInput = {
  and?: InputMaybe<Array<AnnualDividendDtoFilterInput>>;
  currency?: InputMaybe<StringOperationFilterInput>;
  exchangeCurrency?: InputMaybe<StringOperationFilterInput>;
  instrumentId?: InputMaybe<IntOperationFilterInput>;
  or?: InputMaybe<Array<AnnualDividendDtoFilterInput>>;
  total?: InputMaybe<DecimalOperationFilterInput>;
  year?: InputMaybe<IntOperationFilterInput>;
  yield?: InputMaybe<DecimalOperationFilterInput>;
};

export type AnnualDividendDtoSortInput = {
  currency?: InputMaybe<SortEnumType>;
  exchangeCurrency?: InputMaybe<SortEnumType>;
  instrumentId?: InputMaybe<SortEnumType>;
  total?: InputMaybe<SortEnumType>;
  year?: InputMaybe<SortEnumType>;
  yield?: InputMaybe<SortEnumType>;
};

export type Attachment = {
  __typename?: 'Attachment';
  fileName?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  isInBlob: Scalars['Boolean']['output'];
  languageId: Scalars['Int']['output'];
  location?: Maybe<Scalars['String']['output']>;
  mime: Scalars['String']['output'];
  order: Scalars['Byte']['output'];
  pressRelease: PressRelease;
  pressreleaseId: Scalars['Long']['output'];
};


export type AttachmentPressReleaseArgs = {
  groupByMessageType?: InputMaybe<Scalars['Boolean']['input']>;
};

/** A connection to a list of items. */
export type AttachmentConnection = {
  __typename?: 'AttachmentConnection';
  /** A list of edges. */
  edges?: Maybe<Array<AttachmentEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Attachment>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

export type AttachmentDtoFilterInput = {
  and?: InputMaybe<Array<AttachmentDtoFilterInput>>;
  fileName?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<LongOperationFilterInput>;
  isInBlob?: InputMaybe<BooleanOperationFilterInput>;
  languageId?: InputMaybe<IntOperationFilterInput>;
  location?: InputMaybe<StringOperationFilterInput>;
  mime?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<AttachmentDtoFilterInput>>;
  order?: InputMaybe<ByteOperationFilterInput>;
  pressreleaseId?: InputMaybe<LongOperationFilterInput>;
};

export type AttachmentDtoSortInput = {
  order?: InputMaybe<SortEnumType>;
};

/** An edge in a connection. */
export type AttachmentEdge = {
  __typename?: 'AttachmentEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: Attachment;
};

export type Attachment_Deprecated = {
  __typename?: 'Attachment_Deprecated';
  fileName?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  isInBlob: Scalars['Boolean']['output'];
  languageId: Scalars['Int']['output'];
  location?: Maybe<Scalars['String']['output']>;
  mime: Scalars['String']['output'];
  order?: Maybe<Scalars['Byte']['output']>;
  pressreleaseId: Scalars['Long']['output'];
};

/** A connection to a list of items. */
export type AttachmentsConnection = {
  __typename?: 'AttachmentsConnection';
  /** A list of edges. */
  edges?: Maybe<Array<AttachmentsEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<FinancialEventAttachmentDto>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type AttachmentsEdge = {
  __typename?: 'AttachmentsEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: FinancialEventAttachmentDto;
};

export type BalanceSheet = {
  __typename?: 'BalanceSheet';
  acceptedDate?: Maybe<Scalars['Date']['output']>;
  accountPayables?: Maybe<Scalars['Float']['output']>;
  accumulatedOtherComprehensiveIncomeLoss?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  capitalLeaseObligations?: Maybe<Scalars['Float']['output']>;
  cashAndCashEquivalents?: Maybe<Scalars['Float']['output']>;
  cashAndShortTermInvestments?: Maybe<Scalars['Float']['output']>;
  cik?: Maybe<Scalars['String']['output']>;
  commonStock?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  deferredRevenue?: Maybe<Scalars['Float']['output']>;
  deferredRevenueNonCurrent?: Maybe<Scalars['Float']['output']>;
  deferredTaxLiabilitiesNonCurrent?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['Date']['output']>;
  finalLink?: Maybe<Scalars['String']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  goodwill?: Maybe<Scalars['Float']['output']>;
  goodwillAndIntangibleAssets?: Maybe<Scalars['Float']['output']>;
  intangibleAssets?: Maybe<Scalars['Float']['output']>;
  inventory?: Maybe<Scalars['Float']['output']>;
  link?: Maybe<Scalars['String']['output']>;
  longTermDebt?: Maybe<Scalars['Float']['output']>;
  longTermInvestments?: Maybe<Scalars['Float']['output']>;
  minorityInterest?: Maybe<Scalars['Float']['output']>;
  netDebt?: Maybe<Scalars['Float']['output']>;
  netReceivables?: Maybe<Scalars['Float']['output']>;
  otherAssets?: Maybe<Scalars['Float']['output']>;
  otherCurrentAssets?: Maybe<Scalars['Float']['output']>;
  otherCurrentLiabilities?: Maybe<Scalars['Float']['output']>;
  otherLiabilities?: Maybe<Scalars['Float']['output']>;
  otherNonCurrentAssets?: Maybe<Scalars['Float']['output']>;
  otherNonCurrentLiabilities?: Maybe<Scalars['Float']['output']>;
  othertotalStockholdersEquity?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  preferredStock?: Maybe<Scalars['Float']['output']>;
  propertyPlantEquipmentNet?: Maybe<Scalars['Float']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  retainedEarnings?: Maybe<Scalars['Float']['output']>;
  shortTermDebt?: Maybe<Scalars['Float']['output']>;
  shortTermInvestments?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  taxAssets?: Maybe<Scalars['Float']['output']>;
  taxPayables?: Maybe<Scalars['Float']['output']>;
  totalAssets?: Maybe<Scalars['Float']['output']>;
  totalCurrentAssets?: Maybe<Scalars['Float']['output']>;
  totalCurrentLiabilities?: Maybe<Scalars['Float']['output']>;
  totalDebt?: Maybe<Scalars['Float']['output']>;
  totalEquity?: Maybe<Scalars['Float']['output']>;
  totalInvestments?: Maybe<Scalars['Float']['output']>;
  totalLiabilities?: Maybe<Scalars['Float']['output']>;
  totalLiabilitiesAndStockholdersEquity?: Maybe<Scalars['Float']['output']>;
  totalLiabilitiesAndTotalEquity?: Maybe<Scalars['Float']['output']>;
  totalNonCurrentAssets?: Maybe<Scalars['Float']['output']>;
  totalNonCurrentLiabilities?: Maybe<Scalars['Float']['output']>;
  totalStockholdersEquity?: Maybe<Scalars['Float']['output']>;
};

/** A segment of a collection. */
export type BalanceSheetCollectionSegment = {
  __typename?: 'BalanceSheetCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<BalanceSheet>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type BalanceSheetGrowth = {
  __typename?: 'BalanceSheetGrowth';
  calendarYear?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  fillingDate?: Maybe<Scalars['String']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  growthAccountPayables?: Maybe<Scalars['Float']['output']>;
  growthAccumulatedOtherComprehensiveIncomeLoss?: Maybe<Scalars['Float']['output']>;
  growthCashAndCashEquivalents?: Maybe<Scalars['Float']['output']>;
  growthCashAndShortTermInvestments?: Maybe<Scalars['Float']['output']>;
  growthCommonStock?: Maybe<Scalars['Float']['output']>;
  growthDeferredRevenue?: Maybe<Scalars['Float']['output']>;
  growthDeferredRevenueNonCurrent?: Maybe<Scalars['Float']['output']>;
  growthDeferrredTaxLiabilitiesNonCurrent?: Maybe<Scalars['Float']['output']>;
  growthGoodwill?: Maybe<Scalars['Float']['output']>;
  growthGoodwillAndIntangibleAssets?: Maybe<Scalars['Float']['output']>;
  growthIntangibleAssets?: Maybe<Scalars['Float']['output']>;
  growthInventory?: Maybe<Scalars['Float']['output']>;
  growthLongTermDebt?: Maybe<Scalars['Float']['output']>;
  growthLongTermInvestments?: Maybe<Scalars['Float']['output']>;
  growthNetDebt?: Maybe<Scalars['Float']['output']>;
  growthNetReceivables?: Maybe<Scalars['Float']['output']>;
  growthOtherAssets?: Maybe<Scalars['Float']['output']>;
  growthOtherCurrentAssets?: Maybe<Scalars['Float']['output']>;
  growthOtherCurrentLiabilities?: Maybe<Scalars['Float']['output']>;
  growthOtherLiabilities?: Maybe<Scalars['Float']['output']>;
  growthOtherNonCurrentAssets?: Maybe<Scalars['Float']['output']>;
  growthOtherNonCurrentLiabilities?: Maybe<Scalars['Float']['output']>;
  growthOthertotalStockholdersEquity?: Maybe<Scalars['Float']['output']>;
  growthPropertyPlantEquipmentNet?: Maybe<Scalars['Float']['output']>;
  growthRetainedEarnings?: Maybe<Scalars['Float']['output']>;
  growthShortTermDebt?: Maybe<Scalars['Float']['output']>;
  growthShortTermInvestments?: Maybe<Scalars['Float']['output']>;
  growthTaxAssets?: Maybe<Scalars['Float']['output']>;
  growthTaxPayables?: Maybe<Scalars['Float']['output']>;
  growthTotalAssets?: Maybe<Scalars['Float']['output']>;
  growthTotalCurrentAssets?: Maybe<Scalars['Float']['output']>;
  growthTotalCurrentLiabilities?: Maybe<Scalars['Float']['output']>;
  growthTotalDebt?: Maybe<Scalars['Float']['output']>;
  growthTotalInvestments?: Maybe<Scalars['Float']['output']>;
  growthTotalLiabilities?: Maybe<Scalars['Float']['output']>;
  growthTotalLiabilitiesAndStockholdersEquity?: Maybe<Scalars['Float']['output']>;
  growthTotalNonCurrentAssets?: Maybe<Scalars['Float']['output']>;
  growthTotalNonCurrentLiabilities?: Maybe<Scalars['Float']['output']>;
  growthTotalStockholdersEquity?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
};

/** A segment of a collection. */
export type BalanceSheetGrowthCollectionSegment = {
  __typename?: 'BalanceSheetGrowthCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<BalanceSheetGrowth>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type BooleanOperationFilterInput = {
  eq?: InputMaybe<Scalars['Boolean']['input']>;
  neq?: InputMaybe<Scalars['Boolean']['input']>;
};

export type ByteOperationFilterInput = {
  eq?: InputMaybe<Scalars['Byte']['input']>;
  gt?: InputMaybe<Scalars['Byte']['input']>;
  gte?: InputMaybe<Scalars['Byte']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Byte']['input']>>>;
  lt?: InputMaybe<Scalars['Byte']['input']>;
  lte?: InputMaybe<Scalars['Byte']['input']>;
  neq?: InputMaybe<Scalars['Byte']['input']>;
  ngt?: InputMaybe<Scalars['Byte']['input']>;
  ngte?: InputMaybe<Scalars['Byte']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['Byte']['input']>>>;
  nlt?: InputMaybe<Scalars['Byte']['input']>;
  nlte?: InputMaybe<Scalars['Byte']['input']>;
};

export type CashFlowGrowth = {
  __typename?: 'CashFlowGrowth';
  calendarYear?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  fillingDate?: Maybe<Scalars['String']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  growthAccountsPayables?: Maybe<Scalars['Float']['output']>;
  growthAccountsReceivables?: Maybe<Scalars['Float']['output']>;
  growthAcquisitionsNet?: Maybe<Scalars['Float']['output']>;
  growthCapitalExpenditure?: Maybe<Scalars['Float']['output']>;
  growthCashAtBeginningOfPeriod?: Maybe<Scalars['Float']['output']>;
  growthCashAtEndOfPeriod?: Maybe<Scalars['Float']['output']>;
  growthChangeInWorkingCapital?: Maybe<Scalars['Float']['output']>;
  growthCommonStockIssued?: Maybe<Scalars['Float']['output']>;
  growthCommonStockRepurchased?: Maybe<Scalars['Float']['output']>;
  growthDebtRepayment?: Maybe<Scalars['Float']['output']>;
  growthDeferredIncomeTax?: Maybe<Scalars['Float']['output']>;
  growthDepreciationAndAmortization?: Maybe<Scalars['Float']['output']>;
  growthDividendsPaid?: Maybe<Scalars['Float']['output']>;
  growthEffectOfForexChangesOnCash?: Maybe<Scalars['Float']['output']>;
  growthFreeCashFlow?: Maybe<Scalars['Float']['output']>;
  growthInventory?: Maybe<Scalars['Float']['output']>;
  growthInvestmentsInPropertyPlantAndEquipment?: Maybe<Scalars['Float']['output']>;
  growthNetCashProvidedByOperatingActivites?: Maybe<Scalars['Float']['output']>;
  growthNetCashUsedForInvestingActivites?: Maybe<Scalars['Float']['output']>;
  growthNetCashUsedProvidedByFinancingActivities?: Maybe<Scalars['Float']['output']>;
  growthNetChangeInCash?: Maybe<Scalars['Float']['output']>;
  growthNetIncome?: Maybe<Scalars['Float']['output']>;
  growthOperatingCashFlow?: Maybe<Scalars['Float']['output']>;
  growthOtherFinancingActivites?: Maybe<Scalars['Float']['output']>;
  growthOtherInvestingActivites?: Maybe<Scalars['Float']['output']>;
  growthOtherNonCashItems?: Maybe<Scalars['Float']['output']>;
  growthOtherWorkingCapital?: Maybe<Scalars['Float']['output']>;
  growthPurchasesOfInvestments?: Maybe<Scalars['Float']['output']>;
  growthSalesMaturitiesOfInvestments?: Maybe<Scalars['Float']['output']>;
  growthStockBasedCompensation?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
};

/** A segment of a collection. */
export type CashFlowGrowthCollectionSegment = {
  __typename?: 'CashFlowGrowthCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<CashFlowGrowth>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type CashFlowStatement = {
  __typename?: 'CashFlowStatement';
  acceptedDate?: Maybe<Scalars['Date']['output']>;
  accountsPayables?: Maybe<Scalars['Float']['output']>;
  accountsReceivables?: Maybe<Scalars['Float']['output']>;
  acquisitionsNet?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  capitalExpenditure?: Maybe<Scalars['Float']['output']>;
  cashAtBeginningOfPeriod?: Maybe<Scalars['Float']['output']>;
  cashAtEndOfPeriod?: Maybe<Scalars['Float']['output']>;
  changeInWorkingCapital?: Maybe<Scalars['Float']['output']>;
  cik?: Maybe<Scalars['String']['output']>;
  commonStockIssued?: Maybe<Scalars['Float']['output']>;
  commonStockRepurchased?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  debtRepayment?: Maybe<Scalars['Float']['output']>;
  deferredIncomeTax?: Maybe<Scalars['Float']['output']>;
  depreciationAndAmortization?: Maybe<Scalars['Float']['output']>;
  dividendsPaid?: Maybe<Scalars['Float']['output']>;
  effectOfForexChangesOnCash?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['Date']['output']>;
  finalLink?: Maybe<Scalars['String']['output']>;
  freeCashFlow?: Maybe<Scalars['Float']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  inventory?: Maybe<Scalars['Float']['output']>;
  investmentsInPropertyPlantAndEquipment?: Maybe<Scalars['Float']['output']>;
  link?: Maybe<Scalars['String']['output']>;
  netCashProvidedByOperatingActivities?: Maybe<Scalars['Float']['output']>;
  netCashUsedForInvestingActivites?: Maybe<Scalars['Float']['output']>;
  netCashUsedProvidedByFinancingActivities?: Maybe<Scalars['Float']['output']>;
  netChangeInCash?: Maybe<Scalars['Float']['output']>;
  netIncome?: Maybe<Scalars['Float']['output']>;
  operatingCashFlow?: Maybe<Scalars['Float']['output']>;
  otherFinancingActivites?: Maybe<Scalars['Float']['output']>;
  otherInvestingActivites?: Maybe<Scalars['Float']['output']>;
  otherNonCashItems?: Maybe<Scalars['Float']['output']>;
  otherWorkingCapital?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  purchasesOfInvestments?: Maybe<Scalars['Float']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  salesMaturitiesOfInvestments?: Maybe<Scalars['Float']['output']>;
  stockBasedCompensation?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
};

/** A segment of a collection. */
export type CashFlowStatementCollectionSegment = {
  __typename?: 'CashFlowStatementCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<CashFlowStatement>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type City = HasTranslation & {
  __typename?: 'City';
  country?: Maybe<Country>;
  countryId: Scalars['Int']['output'];
  id: Scalars['Int']['output'];
  name?: Maybe<Scalars['String']['output']>;
  translation?: Maybe<Translation>;
  translationId?: Maybe<Scalars['Int']['output']>;
};


export type CityTranslationArgs = {
  cultureName?: InputMaybe<Scalars['String']['input']>;
};

/** Information about the offset pagination. */
export type CollectionSegmentInfo = {
  __typename?: 'CollectionSegmentInfo';
  /** Indicates whether more items exist following the set defined by the clients arguments. */
  hasNextPage: Scalars['Boolean']['output'];
  /** Indicates whether more items exist prior the set defined by the clients arguments. */
  hasPreviousPage: Scalars['Boolean']['output'];
};

/** Represents a company in the Euroland system. */
export type Company = {
  __typename?: 'Company';
  code: Scalars['String']['output'];
  /** @deprecated This field might be removed in the next release. Use field Code instead */
  companyCode?: Maybe<Scalars['String']['output']>;
  /** @deprecated This field might be removed in the next release. Use field Country instead */
  companyCountry?: Maybe<Scalars['String']['output']>;
  /** @deprecated This field might be removed in the next release. Use field CustomerTypeId instead */
  companyCustomer: Scalars['Short']['output'];
  /** @deprecated This field might be removed in the next release. Use field Email instead */
  companyEMail?: Maybe<Scalars['String']['output']>;
  /** @deprecated This field might be removed in the next release. Use field Fax instead */
  companyFax?: Maybe<Scalars['String']['output']>;
  /** @deprecated This field might be removed in the next release. Use field HomePage instead */
  companyHomePage?: Maybe<Scalars['String']['output']>;
  /** @deprecated This field might be removed in the next release. Use field Id instead. */
  companyId: Scalars['Int']['output'];
  /** @deprecated This field might be removed in the next release. Use field MarketId instead */
  companyMarket: Scalars['Short']['output'];
  /** @deprecated This field might be removed in the next release. Use field Name instead */
  companyName?: Maybe<Scalars['String']['output']>;
  /** @deprecated This field might be removed in the next release. Use field Tel instead */
  companyTel?: Maybe<Scalars['String']['output']>;
  /** @deprecated This field might be removed in the next release. Use field Town instead */
  companyTown?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  customerType?: Maybe<CustomerType>;
  customerTypeId: Scalars['Short']['output'];
  email?: Maybe<Scalars['String']['output']>;
  fax?: Maybe<Scalars['String']['output']>;
  /** Get all FCEvent types for the company */
  fcEventTypes?: Maybe<FcEventTypesConnection>;
  /** Get all FCEvents for the company */
  fcEvents?: Maybe<FcEventsConnection>;
  /** Get FCEvents by FCEvent types name in English */
  fcEventsByTypes?: Maybe<FcEventsByTypesConnection>;
  fundamental: Fundamental;
  homePage?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  instruments: Array<Maybe<Instrument>>;
  marketId: Scalars['Short']['output'];
  name?: Maybe<Scalars['String']['output']>;
  /** Gets year range that have announcements. */
  pressReleaseAvailableYearRange?: Maybe<PressReleaseAvailableYearRangeDto>;
  pressReleaseById: PressRelease;
  /** Get all available types of announcements of a company. */
  pressReleaseMessageTypes: Array<PressReleaseMessageType>;
  /** Gets list of pressreleases. Default pageSize=200. If you want to get message of pressrelease, should use `pressReleaseById` query for improving performance. */
  pressReleases?: Maybe<PressReleasesConnection>;
  primaryMarket?: Maybe<Market>;
  tel?: Maybe<Scalars['String']['output']>;
  town?: Maybe<Scalars['String']['output']>;
  translation?: Maybe<CompanyName>;
  /** Gets list of webcasts. Default pageSize = 200 */
  webcasts?: Maybe<WebcastsConnection>;
};


/** Represents a company in the Euroland system. */
export type CompanyFcEventTypesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<FcEventTypeDtoSortInput>>;
  where?: InputMaybe<FcEventTypeDtoFilterInput>;
};


/** Represents a company in the Euroland system. */
export type CompanyFcEventsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<FCalendarDtoSortInput>>;
  where?: InputMaybe<FCalendarDtoFilterInput>;
};


/** Represents a company in the Euroland system. */
export type CompanyFcEventsByTypesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  fcEventTypeNames: Array<InputMaybe<Scalars['String']['input']>>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<FCalendarDtoSortInput>>;
  where?: InputMaybe<FCalendarDtoFilterInput>;
};


/** Represents a company in the Euroland system. */
export type CompanyInstrumentsArgs = {
  adjClose?: InputMaybe<Scalars['Boolean']['input']>;
  exchangeCurrency?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<InstrumentType>;
  useCloud?: InputMaybe<Scalars['Boolean']['input']>;
};


/** Represents a company in the Euroland system. */
export type CompanyPressReleaseAvailableYearRangeArgs = {
  from?: InputMaybe<Scalars['Date']['input']>;
  languageId?: Scalars['Int']['input'];
  sourceId?: InputMaybe<Scalars['Int']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
};


/** Represents a company in the Euroland system. */
export type CompanyPressReleaseByIdArgs = {
  groupByMessageType?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['Long']['input'];
  includeHidden?: InputMaybe<Scalars['Boolean']['input']>;
};


/** Represents a company in the Euroland system. */
export type CompanyPressReleaseMessageTypesArgs = {
  from?: InputMaybe<Scalars['Date']['input']>;
  groupByMessageType?: InputMaybe<Scalars['Boolean']['input']>;
  languageId?: Scalars['Int']['input'];
  sourceId?: InputMaybe<Scalars['Int']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
};


/** Represents a company in the Euroland system. */
export type CompanyPressReleasesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  groupByMessageType?: InputMaybe<Scalars['Boolean']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<PressReleaseDtoSortInput>>;
  where?: InputMaybe<PressReleaseDtoFilterInput>;
};


/** Represents a company in the Euroland system. */
export type CompanyTranslationArgs = {
  cultureName: Scalars['String']['input'];
};


/** Represents a company in the Euroland system. */
export type CompanyWebcastsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<WebcastDtoSortInput>>;
  where?: InputMaybe<WebcastDtoFilterInput>;
};

export type CompanyName = {
  __typename?: 'CompanyName';
  address1?: Maybe<Scalars['String']['output']>;
  address2?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  eMail?: Maybe<Scalars['String']['output']>;
  fax?: Maybe<Scalars['String']['output']>;
  language: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
  tel?: Maybe<Scalars['String']['output']>;
  town?: Maybe<Scalars['String']['output']>;
  zip?: Maybe<Scalars['String']['output']>;
};

export type CompareData = {
  __typename?: 'CompareData';
  changePercentage?: Maybe<Scalars['Decimal']['output']>;
  dateTime: Scalars['DateTime']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type ConvertCurrencyRate_Deprecated = {
  __typename?: 'ConvertCurrencyRate_Deprecated';
  baseCurrency: Scalars['String']['output'];
  date?: Maybe<Scalars['DateTime']['output']>;
  quoteCurrency: Scalars['String']['output'];
  value: Scalars['Decimal']['output'];
};

export type Country = HasTranslation & {
  __typename?: 'Country';
  callingCode: Scalars['Short']['output'];
  id: Scalars['Int']['output'];
  isEnabled: Scalars['Boolean']['output'];
  name?: Maybe<Scalars['String']['output']>;
  translation?: Maybe<Translation>;
  translationId?: Maybe<Scalars['Int']['output']>;
};


export type CountryTranslationArgs = {
  cultureName?: InputMaybe<Scalars['String']['input']>;
};

export type Currency = HasTranslation & {
  __typename?: 'Currency';
  code: Scalars['String']['output'];
  /** @deprecated No longer supported. */
  currencies: Array<Currency_Deprecated>;
  /** @deprecated No longer supported. */
  currenciesByCodes: Array<Currency_Deprecated>;
  /** @deprecated No longer supported. */
  currentRate: CurrencyRate_Deprecated;
  /** @deprecated No longer supported. */
  currentRates: Array<ConvertCurrencyRate_Deprecated>;
  decimalPlace?: Maybe<Scalars['Int']['output']>;
  /** @deprecated No longer supported. */
  historicalRates: Array<CurrencyRate_Deprecated>;
  isRegionMajor?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
  regionId?: Maybe<Scalars['Byte']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  translation?: Maybe<Translation>;
  translationId?: Maybe<Scalars['Int']['output']>;
};


export type CurrencyCurrenciesByCodesArgs = {
  codes: Array<InputMaybe<Scalars['String']['input']>>;
};


export type CurrencyCurrentRateArgs = {
  baseCurrency: Scalars['String']['input'];
  isRT?: Scalars['Boolean']['input'];
  quoteCurrency: Scalars['String']['input'];
};


export type CurrencyCurrentRatesArgs = {
  baseCurrency: Scalars['String']['input'];
  isRT?: Scalars['Boolean']['input'];
  quoteCurrency: Scalars['String']['input'];
};


export type CurrencyHistoricalRatesArgs = {
  baseCurrency: Scalars['String']['input'];
  fromDateUTC: Scalars['DateTime']['input'];
  isRT?: Scalars['Boolean']['input'];
  quoteCurrency: Scalars['String']['input'];
  toDateUTC?: InputMaybe<Scalars['DateTime']['input']>;
};


export type CurrencyTranslationArgs = {
  cultureName?: InputMaybe<Scalars['String']['input']>;
};

export type CurrencyQueries_Deprecated = {
  __typename?: 'CurrencyQueries_Deprecated';
  currencies: Array<Currency_Deprecated>;
  currenciesByCodes: Array<Currency_Deprecated>;
  currentRate: CurrencyRate_Deprecated;
  currentRates: Array<ConvertCurrencyRate_Deprecated>;
  historicalRates: Array<CurrencyRate_Deprecated>;
};


export type CurrencyQueries_DeprecatedCurrenciesByCodesArgs = {
  codes: Array<Scalars['String']['input']>;
};


export type CurrencyQueries_DeprecatedCurrentRateArgs = {
  baseCurrency: Scalars['String']['input'];
  isRT?: Scalars['Boolean']['input'];
  quoteCurrency: Scalars['String']['input'];
};


export type CurrencyQueries_DeprecatedCurrentRatesArgs = {
  baseCurrencies: Array<Scalars['String']['input']>;
  isRT?: Scalars['Boolean']['input'];
  quoteCurrency: Scalars['String']['input'];
};


export type CurrencyQueries_DeprecatedHistoricalRatesArgs = {
  baseCurrency: Scalars['String']['input'];
  fromDateUTC: Scalars['DateTime']['input'];
  isRT?: Scalars['Boolean']['input'];
  quoteCurrency: Scalars['String']['input'];
  toDateUTC?: InputMaybe<Scalars['DateTime']['input']>;
};

export type CurrencyRate = {
  __typename?: 'CurrencyRate';
  date?: Maybe<Scalars['DateTime']['output']>;
  historicals?: Maybe<CurrencyRateHistoryConnection>;
  id: Scalars['Int']['output'];
  pair: Scalars['String']['output'];
  value?: Maybe<Scalars['Decimal']['output']>;
};


export type CurrencyRateHistoricalsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<CurrencyRateHistoryDtoSortInput>>;
  where?: InputMaybe<CurrencyRateHistoryDtoFilterInput>;
};

export type CurrencyRateHistory = {
  __typename?: 'CurrencyRateHistory';
  currencyRate?: Maybe<CurrencyRate>;
  currencyRateId: Scalars['Int']['output'];
  date: Scalars['DateTime']['output'];
  rate: Scalars['Decimal']['output'];
};

/** A connection to a list of items. */
export type CurrencyRateHistoryConnection = {
  __typename?: 'CurrencyRateHistoryConnection';
  /** A list of edges. */
  edges?: Maybe<Array<CurrencyRateHistoryEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<CurrencyRateHistory>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

export type CurrencyRateHistoryDtoFilterInput = {
  and?: InputMaybe<Array<CurrencyRateHistoryDtoFilterInput>>;
  currencyRateId?: InputMaybe<IntOperationFilterInput>;
  date?: InputMaybe<DateTimeOperationFilterInput>;
  or?: InputMaybe<Array<CurrencyRateHistoryDtoFilterInput>>;
  rate?: InputMaybe<DecimalOperationFilterInput>;
};

export type CurrencyRateHistoryDtoSortInput = {
  date?: InputMaybe<SortEnumType>;
};

/** An edge in a connection. */
export type CurrencyRateHistoryEdge = {
  __typename?: 'CurrencyRateHistoryEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: CurrencyRateHistory;
};

export type CurrencyRate_Deprecated = {
  __typename?: 'CurrencyRate_Deprecated';
  date?: Maybe<Scalars['DateTime']['output']>;
  value: Scalars['Decimal']['output'];
};

export type Currency_Deprecated = {
  __typename?: 'Currency_Deprecated';
  code: Scalars['String']['output'];
  decimalPlace?: Maybe<Scalars['Int']['output']>;
  isRegionMajor?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
  regionId?: Maybe<Scalars['Byte']['output']>;
  translationId?: Maybe<Scalars['Int']['output']>;
};

export type CustomerType = {
  __typename?: 'CustomerType';
  id: Scalars['Byte']['output'];
  type?: Maybe<Scalars['String']['output']>;
};

export type DateTimeOperationFilterInput = {
  eq?: InputMaybe<Scalars['DateTime']['input']>;
  gt?: InputMaybe<Scalars['DateTime']['input']>;
  gte?: InputMaybe<Scalars['DateTime']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['DateTime']['input']>>>;
  lt?: InputMaybe<Scalars['DateTime']['input']>;
  lte?: InputMaybe<Scalars['DateTime']['input']>;
  neq?: InputMaybe<Scalars['DateTime']['input']>;
  ngt?: InputMaybe<Scalars['DateTime']['input']>;
  ngte?: InputMaybe<Scalars['DateTime']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['DateTime']['input']>>>;
  nlt?: InputMaybe<Scalars['DateTime']['input']>;
  nlte?: InputMaybe<Scalars['DateTime']['input']>;
};

export type DecimalOperationFilterInput = {
  eq?: InputMaybe<Scalars['Decimal']['input']>;
  gt?: InputMaybe<Scalars['Decimal']['input']>;
  gte?: InputMaybe<Scalars['Decimal']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Decimal']['input']>>>;
  lt?: InputMaybe<Scalars['Decimal']['input']>;
  lte?: InputMaybe<Scalars['Decimal']['input']>;
  neq?: InputMaybe<Scalars['Decimal']['input']>;
  ngt?: InputMaybe<Scalars['Decimal']['input']>;
  ngte?: InputMaybe<Scalars['Decimal']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['Decimal']['input']>>>;
  nlt?: InputMaybe<Scalars['Decimal']['input']>;
  nlte?: InputMaybe<Scalars['Decimal']['input']>;
};

export type DisplayDataForTickerDto = {
  __typename?: 'DisplayDataForTickerDto';
  change?: Maybe<Scalars['Decimal']['output']>;
  changePercentage?: Maybe<Scalars['Decimal']['output']>;
  last?: Maybe<Scalars['Decimal']['output']>;
  prevClose?: Maybe<Scalars['Decimal']['output']>;
};

/** Dividend per share */
export type Dividend = {
  __typename?: 'Dividend';
  basicEarningsPerShare?: Maybe<Scalars['Long']['output']>;
  /** Original dividend currency */
  currency?: Maybe<Scalars['String']['output']>;
  dividendAmount?: Maybe<Scalars['Long']['output']>;
  enabled: Scalars['Boolean']['output'];
  exDate?: Maybe<Scalars['DateTime']['output']>;
  /** Exchange currency (if provided) that price is converted from original dividend currency into this */
  exchangeCurrency?: Maybe<Scalars['String']['output']>;
  fyeDate?: Maybe<Scalars['DateTime']['output']>;
  grossDivAdj?: Maybe<Scalars['Decimal']['output']>;
  grossDividend?: Maybe<Scalars['Decimal']['output']>;
  instrument: Instrument;
  instrumentId: Scalars['Int']['output'];
  lastRowChange: Scalars['DateTime']['output'];
  netDivAdj?: Maybe<Scalars['Decimal']['output']>;
  netDividend?: Maybe<Scalars['Decimal']['output']>;
  payDate?: Maybe<Scalars['DateTime']['output']>;
  payType?: Maybe<Scalars['String']['output']>;
  payoutPer?: Maybe<Scalars['Decimal']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  recDate?: Maybe<Scalars['DateTime']['output']>;
  shareCapital?: Maybe<Scalars['Long']['output']>;
  splitNr?: Maybe<Scalars['Decimal']['output']>;
};

export type DividendDtoFilterInput = {
  and?: InputMaybe<Array<DividendDtoFilterInput>>;
  basicEarningsPerShare?: InputMaybe<LongOperationFilterInput>;
  currency?: InputMaybe<StringOperationFilterInput>;
  dividendAmount?: InputMaybe<LongOperationFilterInput>;
  enabled?: InputMaybe<BooleanOperationFilterInput>;
  exDate?: InputMaybe<DateTimeOperationFilterInput>;
  exchangeCurrency?: InputMaybe<StringOperationFilterInput>;
  fyeDate?: InputMaybe<DateTimeOperationFilterInput>;
  grossDivAdj?: InputMaybe<DecimalOperationFilterInput>;
  grossDividend?: InputMaybe<DecimalOperationFilterInput>;
  instrumentId?: InputMaybe<IntOperationFilterInput>;
  lastRowChange?: InputMaybe<DateTimeOperationFilterInput>;
  netDivAdj?: InputMaybe<DecimalOperationFilterInput>;
  netDividend?: InputMaybe<DecimalOperationFilterInput>;
  or?: InputMaybe<Array<DividendDtoFilterInput>>;
  payDate?: InputMaybe<DateTimeOperationFilterInput>;
  payType?: InputMaybe<StringOperationFilterInput>;
  payoutPer?: InputMaybe<DecimalOperationFilterInput>;
  period?: InputMaybe<StringOperationFilterInput>;
  recDate?: InputMaybe<DateTimeOperationFilterInput>;
  shareCapital?: InputMaybe<LongOperationFilterInput>;
  splitNr?: InputMaybe<DecimalOperationFilterInput>;
};

export type DividendDtoSortInput = {
  exDate?: InputMaybe<SortEnumType>;
};

export type DividendEvent_Deprecated = {
  __typename?: 'DividendEvent_Deprecated';
  currency: Scalars['String']['output'];
  date: Scalars['String']['output'];
  dateTime?: Maybe<Scalars['DateTime']['output']>;
  dividend: Scalars['Decimal']['output'];
};

/** A connection to a list of items. */
export type DividendsConnection = {
  __typename?: 'DividendsConnection';
  /** A list of edges. */
  edges?: Maybe<Array<DividendsEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Dividend>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type DividendsEdge = {
  __typename?: 'DividendsEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: Dividend;
};

export type EarningEvent_Deprecated = {
  __typename?: 'EarningEvent_Deprecated';
  date?: Maybe<Scalars['String']['output']>;
  dateTime?: Maybe<Scalars['DateTime']['output']>;
  heading: Scalars['String']['output'];
};

export type EstimatesFilterInput = {
  calculation?: InputMaybe<EstimatesStringFieldFilterInput>;
  period?: InputMaybe<TimeStringFieldFilterInput>;
  year?: InputMaybe<TimeIntFieldFilterInput>;
};

export type EstimatesStringFieldFilterInput = {
  eq?: InputMaybe<Scalars['String']['input']>;
};

export type FcEvent = {
  __typename?: 'FCEvent';
  companyCode: Scalars['String']['output'];
  dateTime?: Maybe<Scalars['DateTime']['output']>;
  eventName: Scalars['String']['output'];
  fCalendarId: Scalars['Int']['output'];
  fcEventId?: Maybe<Scalars['Short']['output']>;
  fyEndMonth?: Maybe<Scalars['String']['output']>;
  lastUpdated?: Maybe<Scalars['DateTime']['output']>;
  locationId?: Maybe<Scalars['Short']['output']>;
  market?: Maybe<Market>;
  marketId?: Maybe<Scalars['Short']['output']>;
  monthOnly?: Maybe<Scalars['Boolean']['output']>;
  timezone?: Maybe<Timezone>;
  timezoneId?: Maybe<Scalars['Short']['output']>;
};

export type FcEventType = {
  __typename?: 'FCEventType';
  dKtranslated?: Maybe<Scalars['Boolean']['output']>;
  eTtranslated?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['Short']['output'];
  jPtranslated?: Maybe<Scalars['Boolean']['output']>;
  kOtranslated?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  vItranslated?: Maybe<Scalars['Boolean']['output']>;
};

export type FcEventTypeDtoFilterInput = {
  and?: InputMaybe<Array<FcEventTypeDtoFilterInput>>;
  dKtranslated?: InputMaybe<BooleanOperationFilterInput>;
  eTtranslated?: InputMaybe<BooleanOperationFilterInput>;
  id?: InputMaybe<ShortOperationFilterInput>;
  jPtranslated?: InputMaybe<BooleanOperationFilterInput>;
  kOtranslated?: InputMaybe<BooleanOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<FcEventTypeDtoFilterInput>>;
  vItranslated?: InputMaybe<BooleanOperationFilterInput>;
};

export type FcEventTypeDtoSortInput = {
  dKtranslated?: InputMaybe<SortEnumType>;
  eTtranslated?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  jPtranslated?: InputMaybe<SortEnumType>;
  kOtranslated?: InputMaybe<SortEnumType>;
  name?: InputMaybe<SortEnumType>;
  vItranslated?: InputMaybe<SortEnumType>;
};

/** A connection to a list of items. */
export type FcEventTypesConnection = {
  __typename?: 'FCEventTypesConnection';
  /** A list of edges. */
  edges?: Maybe<Array<FcEventTypesEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<FcEventType>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type FcEventTypesEdge = {
  __typename?: 'FCEventTypesEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: FcEventType;
};

/** A connection to a list of items. */
export type FcEventsByTypesConnection = {
  __typename?: 'FCEventsByTypesConnection';
  /** A list of edges. */
  edges?: Maybe<Array<FcEventsByTypesEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<FcEvent>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type FcEventsByTypesEdge = {
  __typename?: 'FCEventsByTypesEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: FcEvent;
};

/** A connection to a list of items. */
export type FcEventsConnection = {
  __typename?: 'FCEventsConnection';
  /** A list of edges. */
  edges?: Maybe<Array<FcEventsEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<FcEvent>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type FcEventsEdge = {
  __typename?: 'FCEventsEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: FcEvent;
};

export type FCalendarDtoFilterInput = {
  and?: InputMaybe<Array<FCalendarDtoFilterInput>>;
  companyCode?: InputMaybe<StringOperationFilterInput>;
  dateTime?: InputMaybe<DateTimeOperationFilterInput>;
  eventName?: InputMaybe<StringOperationFilterInput>;
  fCalendarId?: InputMaybe<IntOperationFilterInput>;
  fcEventId?: InputMaybe<ShortOperationFilterInput>;
  fyEndMonth?: InputMaybe<StringOperationFilterInput>;
  lastUpdated?: InputMaybe<DateTimeOperationFilterInput>;
  locationId?: InputMaybe<ShortOperationFilterInput>;
  marketId?: InputMaybe<ShortOperationFilterInput>;
  monthOnly?: InputMaybe<BooleanOperationFilterInput>;
  or?: InputMaybe<Array<FCalendarDtoFilterInput>>;
  timezoneId?: InputMaybe<ShortOperationFilterInput>;
};

export type FCalendarDtoSortInput = {
  dateTime?: InputMaybe<SortEnumType>;
};

export type FinancialEvent = {
  __typename?: 'FinancialEvent';
  /** Get all attachments for the event */
  attachments?: Maybe<AttachmentsConnection>;
  companyCode: Scalars['String']['output'];
  description?: Maybe<Scalars['String']['output']>;
  endDate?: Maybe<Scalars['DateTime']['output']>;
  eventDetailId: Scalars['Int']['output'];
  eventId: Scalars['Int']['output'];
  eventTypeId: Scalars['Int']['output'];
  isAllDayEvent: Scalars['Boolean']['output'];
  isDeleted: Scalars['Boolean']['output'];
  isHighlighted: Scalars['Boolean']['output'];
  languageId: Scalars['Int']['output'];
  lastUpdated?: Maybe<Scalars['DateTime']['output']>;
  linkDescription?: Maybe<Scalars['String']['output']>;
  linkUrl?: Maybe<Scalars['String']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  startDate: Scalars['DateTime']['output'];
  statistics?: Maybe<FinancialEventStatisticsDto>;
  title: Scalars['String']['output'];
};


export type FinancialEventAttachmentsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<FinancialEventAttachmentDtoSortInput>>;
  where?: InputMaybe<FinancialEventAttachmentDtoFilterInput>;
};

export type FinancialEventAttachmentDto = {
  __typename?: 'FinancialEventAttachmentDto';
  fileName: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  location: Scalars['String']['output'];
};

export type FinancialEventAttachmentDtoFilterInput = {
  and?: InputMaybe<Array<FinancialEventAttachmentDtoFilterInput>>;
  fileName?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<IntOperationFilterInput>;
  location?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<FinancialEventAttachmentDtoFilterInput>>;
};

export type FinancialEventAttachmentDtoSortInput = {
  fileName?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  location?: InputMaybe<SortEnumType>;
};

export type FinancialEventDetailDtoFilterInput = {
  and?: InputMaybe<Array<FinancialEventDetailDtoFilterInput>>;
  companyCode?: InputMaybe<StringOperationFilterInput>;
  description?: InputMaybe<StringOperationFilterInput>;
  endDate?: InputMaybe<DateTimeOperationFilterInput>;
  eventDetailId?: InputMaybe<IntOperationFilterInput>;
  eventId?: InputMaybe<IntOperationFilterInput>;
  eventTypeId?: InputMaybe<IntOperationFilterInput>;
  isAllDayEvent?: InputMaybe<BooleanOperationFilterInput>;
  isDeleted?: InputMaybe<BooleanOperationFilterInput>;
  isHighlighted?: InputMaybe<BooleanOperationFilterInput>;
  languageId?: InputMaybe<IntOperationFilterInput>;
  lastUpdated?: InputMaybe<DateTimeOperationFilterInput>;
  linkDescription?: InputMaybe<StringOperationFilterInput>;
  linkUrl?: InputMaybe<StringOperationFilterInput>;
  location?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<FinancialEventDetailDtoFilterInput>>;
  startDate?: InputMaybe<DateTimeOperationFilterInput>;
  title?: InputMaybe<StringOperationFilterInput>;
};

export type FinancialEventDetailDtoSortInput = {
  companyCode?: InputMaybe<SortEnumType>;
  description?: InputMaybe<SortEnumType>;
  endDate?: InputMaybe<SortEnumType>;
  eventDetailId?: InputMaybe<SortEnumType>;
  eventId?: InputMaybe<SortEnumType>;
  eventTypeId?: InputMaybe<SortEnumType>;
  isAllDayEvent?: InputMaybe<SortEnumType>;
  isDeleted?: InputMaybe<SortEnumType>;
  isHighlighted?: InputMaybe<SortEnumType>;
  languageId?: InputMaybe<SortEnumType>;
  lastUpdated?: InputMaybe<SortEnumType>;
  linkDescription?: InputMaybe<SortEnumType>;
  linkUrl?: InputMaybe<SortEnumType>;
  location?: InputMaybe<SortEnumType>;
  startDate?: InputMaybe<SortEnumType>;
  title?: InputMaybe<SortEnumType>;
};

export type FinancialEventStatisticsDto = {
  __typename?: 'FinancialEventStatisticsDto';
  downloaded?: Maybe<Scalars['Int']['output']>;
  eventId: Scalars['Int']['output'];
};

export type FinancialEventType = {
  __typename?: 'FinancialEventType';
  companyCode: Scalars['String']['output'];
  eventTypeId: Scalars['Int']['output'];
  id: Scalars['Int']['output'];
  isActive: Scalars['Boolean']['output'];
  isCustom: Scalars['Boolean']['output'];
  isDeleted: Scalars['Boolean']['output'];
  languageId: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  order: Scalars['Byte']['output'];
};

export type FinancialEventTypeDetailDtoFilterInput = {
  and?: InputMaybe<Array<FinancialEventTypeDetailDtoFilterInput>>;
  companyCode?: InputMaybe<StringOperationFilterInput>;
  eventTypeId?: InputMaybe<IntOperationFilterInput>;
  id?: InputMaybe<IntOperationFilterInput>;
  isActive?: InputMaybe<BooleanOperationFilterInput>;
  isCustom?: InputMaybe<BooleanOperationFilterInput>;
  isDeleted?: InputMaybe<BooleanOperationFilterInput>;
  languageId?: InputMaybe<IntOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<FinancialEventTypeDetailDtoFilterInput>>;
  order?: InputMaybe<ByteOperationFilterInput>;
};

export type FinancialEventTypeDetailDtoSortInput = {
  companyCode?: InputMaybe<SortEnumType>;
  eventTypeId?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  isActive?: InputMaybe<SortEnumType>;
  isCustom?: InputMaybe<SortEnumType>;
  isDeleted?: InputMaybe<SortEnumType>;
  languageId?: InputMaybe<SortEnumType>;
  name?: InputMaybe<SortEnumType>;
  order?: InputMaybe<SortEnumType>;
};

/** A connection to a list of items. */
export type FinancialEventTypesConnection = {
  __typename?: 'FinancialEventTypesConnection';
  /** A list of edges. */
  edges?: Maybe<Array<FinancialEventTypesEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<FinancialEventType>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type FinancialEventTypesEdge = {
  __typename?: 'FinancialEventTypesEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: FinancialEventType;
};

/** A connection to a list of items. */
export type FinancialEventsConnection = {
  __typename?: 'FinancialEventsConnection';
  /** A list of edges. */
  edges?: Maybe<Array<FinancialEventsEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<FinancialEvent>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type FinancialEventsEdge = {
  __typename?: 'FinancialEventsEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: FinancialEvent;
};

export type FinancialGrowth = {
  __typename?: 'FinancialGrowth';
  assetGrowth?: Maybe<Scalars['Float']['output']>;
  bookValueperShareGrowth?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  debtGrowth?: Maybe<Scalars['Float']['output']>;
  dividendsperShareGrowth?: Maybe<Scalars['Float']['output']>;
  ebitgrowth?: Maybe<Scalars['Float']['output']>;
  epsdilutedGrowth?: Maybe<Scalars['Float']['output']>;
  epsgrowth?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['String']['output']>;
  fiveYDividendperShareGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  fiveYNetIncomeGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  fiveYOperatingCFGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  fiveYRevenueGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  fiveYShareholdersEquityGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  freeCashFlowGrowth?: Maybe<Scalars['Float']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  grossProfitGrowth?: Maybe<Scalars['Float']['output']>;
  inventoryGrowth?: Maybe<Scalars['Float']['output']>;
  netIncomeGrowth?: Maybe<Scalars['Float']['output']>;
  operatingCashFlowGrowth?: Maybe<Scalars['Float']['output']>;
  operatingIncomeGrowth?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  rdexpenseGrowth?: Maybe<Scalars['Float']['output']>;
  receivablesGrowth?: Maybe<Scalars['Float']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  revenueGrowth?: Maybe<Scalars['Float']['output']>;
  sgaexpensesGrowth?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  tenYDividendperShareGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  tenYNetIncomeGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  tenYOperatingCFGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  tenYRevenueGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  tenYShareholdersEquityGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  threeYDividendperShareGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  threeYNetIncomeGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  threeYOperatingCFGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  threeYRevenueGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  threeYShareholdersEquityGrowthPerShare?: Maybe<Scalars['Float']['output']>;
  weightedAverageSharesDilutedGrowth?: Maybe<Scalars['Float']['output']>;
  weightedAverageSharesGrowth?: Maybe<Scalars['Float']['output']>;
};

/** A segment of a collection. */
export type FinancialGrowthCollectionSegment = {
  __typename?: 'FinancialGrowthCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<FinancialGrowth>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type FinancialScore = {
  __typename?: 'FinancialScore';
  altmanZScore?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  ebit?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['Date']['output']>;
  marketCap?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  piotroskiScore?: Maybe<Scalars['Float']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  retainedEarnings?: Maybe<Scalars['Float']['output']>;
  revenue?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  totalAssets?: Maybe<Scalars['Float']['output']>;
  totalLiabilities?: Maybe<Scalars['Float']['output']>;
  workingCapital?: Maybe<Scalars['Float']['output']>;
};

/** A segment of a collection. */
export type FinancialScoreCollectionSegment = {
  __typename?: 'FinancialScoreCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<FinancialScore>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type Fundamental = {
  __typename?: 'Fundamental';
  analystEstimates?: Maybe<AnalystEstimatesCollectionSegment>;
  analystRecommendation?: Maybe<AnalystRecommendationDto>;
  balanceSheet?: Maybe<BalanceSheetCollectionSegment>;
  balanceSheetGrowth?: Maybe<BalanceSheetGrowthCollectionSegment>;
  cashFlowGrowth?: Maybe<CashFlowGrowthCollectionSegment>;
  cashFlowStatement?: Maybe<CashFlowStatementCollectionSegment>;
  company: Company;
  companyCode: Scalars['String']['output'];
  /** Get all event types for the company */
  financialEventTypes?: Maybe<FinancialEventTypesConnection>;
  /** Get all events for the company */
  financialEvents?: Maybe<FinancialEventsConnection>;
  financialGrowth?: Maybe<FinancialGrowthCollectionSegment>;
  financialScore?: Maybe<FinancialScoreCollectionSegment>;
  incomeStatement?: Maybe<IncomeStatementCollectionSegment>;
  incomeStatementGrowth?: Maybe<IncomeStatementGrowthCollectionSegment>;
  keyMetrics?: Maybe<KeyMetricsCollectionSegment>;
  keyMetricsTTM?: Maybe<KeyMetricsTtmCollectionSegment>;
  ownerEarnings?: Maybe<OwnerEarningsCollectionSegment>;
  ratios?: Maybe<RatiosCollectionSegment>;
  ratiosTTM?: Maybe<RatiosTtmCollectionSegment>;
  reports?: Maybe<ReportsConnection>;
  segments?: Maybe<SegmentsCollectionSegment>;
};


export type FundamentalAnalystEstimatesArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<EstimatesFilterInput>;
};


export type FundamentalAnalystRecommendationArgs = {
  showDataFrom: Scalars['DateTime']['input'];
};


export type FundamentalBalanceSheetArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalBalanceSheetGrowthArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalCashFlowGrowthArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalCashFlowStatementArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalFinancialEventTypesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  cultureName?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<FinancialEventTypeDetailDtoSortInput>>;
  where?: InputMaybe<FinancialEventTypeDetailDtoFilterInput>;
};


export type FundamentalFinancialEventsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  cultureName?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<FinancialEventDetailDtoSortInput>>;
  where?: InputMaybe<FinancialEventDetailDtoFilterInput>;
};


export type FundamentalFinancialGrowthArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalFinancialScoreArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalIncomeStatementArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalIncomeStatementGrowthArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalKeyMetricsArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalKeyMetricsTtmArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalOwnerEarningsArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalRatiosArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalRatiosTtmArgs = {
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};


export type FundamentalReportsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<ReportDtoSortInput>>;
  where?: InputMaybe<ReportDtoFilterInput>;
};


export type FundamentalSegmentsArgs = {
  detailKeyFilter?: InputMaybe<Scalars['String']['input']>;
  order?: InputMaybe<Array<TimeSortInputSortInput>>;
  segmentKeyFilter?: InputMaybe<Scalars['String']['input']>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  source: Scalars['String']['input'];
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<TimeFilterInput>;
};

/** Translation object which contains the translated string into various supported languages. */
export type HasTranslation = {
  translation?: Maybe<Translation>;
};


/** Translation object which contains the translated string into various supported languages. */
export type HasTranslationTranslationArgs = {
  cultureName?: InputMaybe<Scalars['String']['input']>;
};

export enum Historical {
  Daily = 'DAILY',
  Monthly = 'MONTHLY',
  Weekly = 'WEEKLY'
}

export type IncomeStatement = {
  __typename?: 'IncomeStatement';
  acceptedDate?: Maybe<Scalars['Date']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  cik?: Maybe<Scalars['String']['output']>;
  costAndExpenses?: Maybe<Scalars['Float']['output']>;
  costOfRevenue?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  depreciationAndAmortization?: Maybe<Scalars['Float']['output']>;
  ebitda?: Maybe<Scalars['Float']['output']>;
  ebitdaratio?: Maybe<Scalars['Float']['output']>;
  eps?: Maybe<Scalars['Float']['output']>;
  epsdiluted?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['Date']['output']>;
  finalLink?: Maybe<Scalars['String']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  generalAndAdministrativeExpenses?: Maybe<Scalars['Float']['output']>;
  grossProfit?: Maybe<Scalars['Float']['output']>;
  grossProfitRatio?: Maybe<Scalars['Float']['output']>;
  incomeBeforeTax?: Maybe<Scalars['Float']['output']>;
  incomeBeforeTaxRatio?: Maybe<Scalars['Float']['output']>;
  incomeTaxExpense?: Maybe<Scalars['Float']['output']>;
  interestExpense?: Maybe<Scalars['Float']['output']>;
  interestIncome?: Maybe<Scalars['Float']['output']>;
  link?: Maybe<Scalars['String']['output']>;
  netIncome?: Maybe<Scalars['Float']['output']>;
  netIncomeRatio?: Maybe<Scalars['Float']['output']>;
  operatingExpenses?: Maybe<Scalars['Float']['output']>;
  operatingIncome?: Maybe<Scalars['Float']['output']>;
  operatingIncomeRatio?: Maybe<Scalars['Float']['output']>;
  otherExpenses?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  researchAndDevelopmentExpenses?: Maybe<Scalars['Float']['output']>;
  revenue?: Maybe<Scalars['Float']['output']>;
  sellingAndMarketingExpenses?: Maybe<Scalars['Float']['output']>;
  sellingGeneralAndAdministrativeExpenses?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  totalOtherIncomeExpensesNet?: Maybe<Scalars['Float']['output']>;
  weightedAverageShsOut?: Maybe<Scalars['Float']['output']>;
  weightedAverageShsOutDil?: Maybe<Scalars['Float']['output']>;
};

/** A segment of a collection. */
export type IncomeStatementCollectionSegment = {
  __typename?: 'IncomeStatementCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<IncomeStatement>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type IncomeStatementGrowth = {
  __typename?: 'IncomeStatementGrowth';
  calendarYear?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  fillingDate?: Maybe<Scalars['String']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  growthCostAndExpenses?: Maybe<Scalars['Float']['output']>;
  growthCostOfRevenue?: Maybe<Scalars['Float']['output']>;
  growthDepreciationAndAmortization?: Maybe<Scalars['Float']['output']>;
  growthEBITDA?: Maybe<Scalars['Float']['output']>;
  growthEBITDARatio?: Maybe<Scalars['Float']['output']>;
  growthEPS?: Maybe<Scalars['Float']['output']>;
  growthEPSDiluted?: Maybe<Scalars['Float']['output']>;
  growthGeneralAndAdministrativeExpenses?: Maybe<Scalars['Float']['output']>;
  growthGrossProfit?: Maybe<Scalars['Float']['output']>;
  growthGrossProfitRatio?: Maybe<Scalars['Float']['output']>;
  growthIncomeBeforeTax?: Maybe<Scalars['Float']['output']>;
  growthIncomeBeforeTaxRatio?: Maybe<Scalars['Float']['output']>;
  growthIncomeTaxExpense?: Maybe<Scalars['Float']['output']>;
  growthInterestExpense?: Maybe<Scalars['Float']['output']>;
  growthNetIncome?: Maybe<Scalars['Float']['output']>;
  growthNetIncomeRatio?: Maybe<Scalars['Float']['output']>;
  growthOperatingExpenses?: Maybe<Scalars['Float']['output']>;
  growthOperatingIncome?: Maybe<Scalars['Float']['output']>;
  growthOperatingIncomeRatio?: Maybe<Scalars['Float']['output']>;
  growthOtherExpenses?: Maybe<Scalars['Float']['output']>;
  growthResearchAndDevelopmentExpenses?: Maybe<Scalars['Float']['output']>;
  growthRevenue?: Maybe<Scalars['Float']['output']>;
  growthSellingAndMarketingExpenses?: Maybe<Scalars['Float']['output']>;
  growthTotalOtherIncomeExpensesNet?: Maybe<Scalars['Float']['output']>;
  growthWeightedAverageShsOut?: Maybe<Scalars['Float']['output']>;
  growthWeightedAverageShsOutDil?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
};

/** A segment of a collection. */
export type IncomeStatementGrowthCollectionSegment = {
  __typename?: 'IncomeStatementGrowthCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<IncomeStatementGrowth>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type Instrument = {
  __typename?: 'Instrument';
  agentupdate?: Maybe<Scalars['DateTime']['output']>;
  allTimeHigh?: Maybe<Scalars['Decimal']['output']>;
  allTimeLow?: Maybe<Scalars['Decimal']['output']>;
  annualDividends: Array<AnnualDividend>;
  betaFactor?: Maybe<Scalars['Decimal']['output']>;
  bookValueOfShare?: Maybe<Scalars['Float']['output']>;
  company?: Maybe<Company>;
  companyCode?: Maybe<Scalars['String']['output']>;
  companyID?: Maybe<Scalars['Int']['output']>;
  correlation?: Maybe<Scalars['Decimal']['output']>;
  currency?: Maybe<Currency>;
  currencyCode?: Maybe<Scalars['String']['output']>;
  currentPrice?: Maybe<InstrumentPrice>;
  customer?: Maybe<Scalars['Byte']['output']>;
  customerType?: Maybe<CustomerType>;
  dataSourceID: Scalars['Int']['output'];
  dividends?: Maybe<DividendsConnection>;
  dps?: Maybe<Scalars['Decimal']['output']>;
  eps?: Maybe<Scalars['Decimal']['output']>;
  eurCat?: Maybe<Scalars['Int']['output']>;
  fiveYearsChange?: Maybe<Scalars['Decimal']['output']>;
  high52W?: Maybe<Scalars['Decimal']['output']>;
  highYTD?: Maybe<Scalars['Decimal']['output']>;
  highest52w?: Maybe<Scalars['Decimal']['output']>;
  historicals?: Maybe<InstrumentHistoryConnection>;
  id: Scalars['Int']['output'];
  instrumentType: InstrumentType;
  intraday?: Maybe<InstrumentIntradayConnection>;
  isin: Scalars['String']['output'];
  /** Get all trades within the last day which has daily data. */
  lastDayIntraday?: Maybe<InstrumentLastDayIntradayConnection>;
  lastRowChange?: Maybe<Scalars['DateTime']['output']>;
  list?: Maybe<List>;
  listID?: Maybe<Scalars['Int']['output']>;
  listedFrom?: Maybe<Scalars['DateTime']['output']>;
  lotSize?: Maybe<Scalars['Int']['output']>;
  low52W?: Maybe<Scalars['Decimal']['output']>;
  lowYTD?: Maybe<Scalars['Decimal']['output']>;
  lowest52w?: Maybe<Scalars['Decimal']['output']>;
  mainIndex?: Maybe<Scalars['Boolean']['output']>;
  marCat?: Maybe<Scalars['Int']['output']>;
  market?: Maybe<Market>;
  marketCapEUR?: Maybe<Scalars['Decimal']['output']>;
  marketID: Scalars['Short']['output'];
  marketSpecSign?: Maybe<Scalars['String']['output']>;
  month?: Maybe<Scalars['Decimal']['output']>;
  netInComeGrowth?: Maybe<Scalars['Float']['output']>;
  netIncome?: Maybe<Scalars['Decimal']['output']>;
  noShares?: Maybe<Scalars['Decimal']['output']>;
  numSharesDate?: Maybe<Scalars['DateTime']['output']>;
  numberOfUnlistedShares?: Maybe<Scalars['Long']['output']>;
  orderDepth?: Maybe<OrderDepth>;
  payoutRatio?: Maybe<Scalars['Float']['output']>;
  percent52W?: Maybe<Scalars['Decimal']['output']>;
  performance?: Maybe<PerformanceShare>;
  prevMid?: Maybe<Scalars['Decimal']['output']>;
  primaryMarket?: Maybe<Scalars['Byte']['output']>;
  regionID?: Maybe<Scalars['Int']['output']>;
  ricCode?: Maybe<Scalars['String']['output']>;
  shareName: Scalars['String']['output'];
  splitRatio?: Maybe<Scalars['Float']['output']>;
  sps?: Maybe<Scalars['Float']['output']>;
  subSector?: Maybe<SubSector>;
  symbol: Scalars['String']['output'];
  threeMonthChange?: Maybe<Scalars['Decimal']['output']>;
  threeMonthHigh?: Maybe<Scalars['Decimal']['output']>;
  threeMonthLow?: Maybe<Scalars['Decimal']['output']>;
  tokyoEurope?: Maybe<Scalars['Short']['output']>;
  totalMarketCap?: Maybe<Scalars['Decimal']['output']>;
  turnover?: Maybe<Scalars['Decimal']['output']>;
  turnoverGrowth?: Maybe<Scalars['Float']['output']>;
  twoWeek?: Maybe<Scalars['Decimal']['output']>;
  volatility?: Maybe<Scalars['Decimal']['output']>;
  volumeTurnover?: Maybe<Scalars['Decimal']['output']>;
  volumeTurnoverUSD?: Maybe<Scalars['Decimal']['output']>;
  week?: Maybe<Scalars['Decimal']['output']>;
  wkn?: Maybe<Scalars['String']['output']>;
  yahooSymbol?: Maybe<Scalars['String']['output']>;
  ytd?: Maybe<Scalars['Decimal']['output']>;
};


export type InstrumentAnnualDividendsArgs = {
  order?: InputMaybe<Array<AnnualDividendDtoSortInput>>;
  where?: InputMaybe<AnnualDividendDtoFilterInput>;
};


export type InstrumentDividendsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<DividendDtoSortInput>>;
  where?: InputMaybe<DividendDtoFilterInput>;
};


export type InstrumentHistoricalsArgs = {
  adjClose?: InputMaybe<Scalars['Boolean']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<InstrumentHistoryDtoSortInput>>;
  where?: InputMaybe<InstrumentHistoryDtoFilterInput>;
};


export type InstrumentIntradayArgs = {
  adjClose?: InputMaybe<Scalars['Boolean']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<InstrumentHistoryDtoSortInput>>;
  timeIntervalGrouping?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<InstrumentHistoryDtoFilterInput>;
};


export type InstrumentLastDayIntradayArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<InstrumentDailyDataDtoSortInput>>;
  where?: InputMaybe<InstrumentDailyDataDtoFilterInput>;
};


export type InstrumentPerformanceArgs = {
  fromDate?: InputMaybe<Scalars['DateTime']['input']>;
  period?: InputMaybe<PerformancePeriod>;
};

export type InstrumentDailyDataDto = {
  __typename?: 'InstrumentDailyDataDto';
  close: Scalars['Decimal']['output'];
  date: Scalars['DateTime']['output'];
  id: Scalars['Long']['output'];
  instrumentId: Scalars['Int']['output'];
  volume?: Maybe<Scalars['Long']['output']>;
};

export type InstrumentDailyDataDtoFilterInput = {
  and?: InputMaybe<Array<InstrumentDailyDataDtoFilterInput>>;
  close?: InputMaybe<DecimalOperationFilterInput>;
  date?: InputMaybe<DateTimeOperationFilterInput>;
  id?: InputMaybe<LongOperationFilterInput>;
  instrumentId?: InputMaybe<IntOperationFilterInput>;
  or?: InputMaybe<Array<InstrumentDailyDataDtoFilterInput>>;
  volume?: InputMaybe<LongOperationFilterInput>;
};

export type InstrumentDailyDataDtoSortInput = {
  close?: InputMaybe<SortEnumType>;
  date?: InputMaybe<SortEnumType>;
  volume?: InputMaybe<SortEnumType>;
};

export type InstrumentDailyData_Deprecated = {
  __typename?: 'InstrumentDailyData_Deprecated';
  close: Scalars['Decimal']['output'];
  date: Scalars['DateTime']['output'];
  id: Scalars['Long']['output'];
  instrumentID: Scalars['Int']['output'];
  volume?: Maybe<Scalars['Long']['output']>;
};

export type InstrumentHistory = {
  __typename?: 'InstrumentHistory';
  close: Scalars['Decimal']['output'];
  dateTime: Scalars['DateTime']['output'];
  high?: Maybe<Scalars['Decimal']['output']>;
  instrument?: Maybe<Instrument>;
  instrumentId: Scalars['Int']['output'];
  low?: Maybe<Scalars['Decimal']['output']>;
  open?: Maybe<Scalars['Decimal']['output']>;
  volume?: Maybe<Scalars['Long']['output']>;
};

/** A connection to a list of items. */
export type InstrumentHistoryConnection = {
  __typename?: 'InstrumentHistoryConnection';
  /** A list of edges. */
  edges?: Maybe<Array<InstrumentHistoryEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<InstrumentHistory>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

export type InstrumentHistoryDtoFilterInput = {
  and?: InputMaybe<Array<InstrumentHistoryDtoFilterInput>>;
  close?: InputMaybe<DecimalOperationFilterInput>;
  dateTime?: InputMaybe<DateTimeOperationFilterInput>;
  high?: InputMaybe<DecimalOperationFilterInput>;
  instrumentId?: InputMaybe<IntOperationFilterInput>;
  low?: InputMaybe<DecimalOperationFilterInput>;
  open?: InputMaybe<DecimalOperationFilterInput>;
  or?: InputMaybe<Array<InstrumentHistoryDtoFilterInput>>;
  volume?: InputMaybe<LongOperationFilterInput>;
};

export type InstrumentHistoryDtoSortInput = {
  close?: InputMaybe<SortEnumType>;
  dateTime?: InputMaybe<SortEnumType>;
  volume?: InputMaybe<SortEnumType>;
};

/** An edge in a connection. */
export type InstrumentHistoryEdge = {
  __typename?: 'InstrumentHistoryEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: InstrumentHistory;
};

export type InstrumentHistory_Deprecated_ = {
  __typename?: 'InstrumentHistory_Deprecated_';
  close: Scalars['Decimal']['output'];
  date?: Maybe<Scalars['String']['output']>;
  dateTime: Scalars['DateTime']['output'];
  high?: Maybe<Scalars['Decimal']['output']>;
  instrumentId: Scalars['Int']['output'];
  low?: Maybe<Scalars['Decimal']['output']>;
  open?: Maybe<Scalars['Decimal']['output']>;
  rate?: Maybe<Scalars['Decimal']['output']>;
  volume?: Maybe<Scalars['Long']['output']>;
};

/** A connection to a list of items. */
export type InstrumentIntradayConnection = {
  __typename?: 'InstrumentIntradayConnection';
  /** A list of edges. */
  edges?: Maybe<Array<InstrumentIntradayEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<InstrumentHistory>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type InstrumentIntradayEdge = {
  __typename?: 'InstrumentIntradayEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: InstrumentHistory;
};

/** A connection to a list of items. */
export type InstrumentLastDayIntradayConnection = {
  __typename?: 'InstrumentLastDayIntradayConnection';
  /** A list of edges. */
  edges?: Maybe<Array<InstrumentLastDayIntradayEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<InstrumentDailyDataDto>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type InstrumentLastDayIntradayEdge = {
  __typename?: 'InstrumentLastDayIntradayEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: InstrumentDailyDataDto;
};

export type InstrumentPrice = {
  __typename?: 'InstrumentPrice';
  ask?: Maybe<Scalars['Decimal']['output']>;
  askSize?: Maybe<Scalars['Int']['output']>;
  bid?: Maybe<Scalars['Decimal']['output']>;
  bidSize?: Maybe<Scalars['Int']['output']>;
  change: Scalars['Decimal']['output'];
  changePercentage: Scalars['Decimal']['output'];
  date?: Maybe<Scalars['DateTime']['output']>;
  high?: Maybe<Scalars['Decimal']['output']>;
  instrument: Instrument;
  instrumentId: Scalars['Int']['output'];
  last?: Maybe<Scalars['Decimal']['output']>;
  lastRowChange?: Maybe<Scalars['DateTime']['output']>;
  low?: Maybe<Scalars['Decimal']['output']>;
  mid?: Maybe<Scalars['Decimal']['output']>;
  midChange: Scalars['Decimal']['output'];
  midChangePercentage: Scalars['Decimal']['output'];
  officialClose?: Maybe<Scalars['Decimal']['output']>;
  officialCloseDate?: Maybe<Scalars['DateTime']['output']>;
  open?: Maybe<Scalars['Decimal']['output']>;
  prevClose?: Maybe<Scalars['Decimal']['output']>;
  /** Recalculate the last price, previous close price, change, change percentage of the instrument for using to get same data as chart. */
  tickerData: DisplayDataForTickerDto;
  todayTurnover?: Maybe<Scalars['Decimal']['output']>;
  volume?: Maybe<Scalars['Long']['output']>;
  vwap?: Maybe<Scalars['Decimal']['output']>;
};

export enum InstrumentType {
  All = 'ALL',
  Commodity = 'COMMODITY',
  CurrencyPair = 'CURRENCY_PAIR',
  Index = 'INDEX',
  Share = 'SHARE'
}

export type InstrumentYearlyPerformancePrice_Deprecated = {
  __typename?: 'InstrumentYearlyPerformancePrice_Deprecated';
  currencyCode?: Maybe<Scalars['String']['output']>;
  instrumentId: Scalars['Int']['output'];
  marketAbbreviation?: Maybe<Scalars['String']['output']>;
  shareName?: Maybe<Scalars['String']['output']>;
  yearlyPerformances?: Maybe<Array<YearlyPerformancePrice>>;
};

export type Instrument_Deprecated = {
  __typename?: 'Instrument_Deprecated';
  aYearAgo?: Maybe<Scalars['Decimal']['output']>;
  agentupdate?: Maybe<Scalars['DateTime']['output']>;
  allTimeHigh?: Maybe<Scalars['Decimal']['output']>;
  allTimeLow?: Maybe<Scalars['Decimal']['output']>;
  ask?: Maybe<Scalars['Decimal']['output']>;
  askSize?: Maybe<Scalars['Int']['output']>;
  betaFactor?: Maybe<Scalars['Decimal']['output']>;
  bid?: Maybe<Scalars['Decimal']['output']>;
  bidSize?: Maybe<Scalars['Int']['output']>;
  bookValueOfShare?: Maybe<Scalars['Float']['output']>;
  businessDaysStoT: Scalars['Boolean']['output'];
  change?: Maybe<Scalars['Decimal']['output']>;
  changePercentage?: Maybe<Scalars['Decimal']['output']>;
  companyCode?: Maybe<Scalars['String']['output']>;
  companyID?: Maybe<Scalars['Int']['output']>;
  correlation?: Maybe<Scalars['Decimal']['output']>;
  countdownToTheClosingBell?: Maybe<Scalars['Int']['output']>;
  countdownToTheOpeningBell?: Maybe<Scalars['Int']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  currencyCode?: Maybe<Scalars['String']['output']>;
  customer?: Maybe<Scalars['Byte']['output']>;
  dPS?: Maybe<Scalars['Decimal']['output']>;
  dataSourceID: Scalars['Int']['output'];
  ePS?: Maybe<Scalars['Decimal']['output']>;
  eurCat?: Maybe<Scalars['Int']['output']>;
  fiveYearsChange?: Maybe<Scalars['Decimal']['output']>;
  high?: Maybe<Scalars['Decimal']['output']>;
  high52W?: Maybe<Scalars['Decimal']['output']>;
  highYTD?: Maybe<Scalars['Decimal']['output']>;
  highest52w?: Maybe<Scalars['Decimal']['output']>;
  iSIN: Scalars['String']['output'];
  industry?: Maybe<Scalars['String']['output']>;
  industryTranslationID?: Maybe<Scalars['Int']['output']>;
  instrumentId: Scalars['Int']['output'];
  instrumentType: Scalars['Byte']['output'];
  last?: Maybe<Scalars['Decimal']['output']>;
  lastRowChange?: Maybe<Scalars['DateTime']['output']>;
  lastUpdatedDate?: Maybe<Scalars['DateTime']['output']>;
  latestDate?: Maybe<Scalars['DateTime']['output']>;
  listID?: Maybe<Scalars['Int']['output']>;
  listName?: Maybe<Scalars['String']['output']>;
  listedFrom?: Maybe<Scalars['DateTime']['output']>;
  lotSize?: Maybe<Scalars['Int']['output']>;
  low?: Maybe<Scalars['Decimal']['output']>;
  low52W?: Maybe<Scalars['Decimal']['output']>;
  lowYTD?: Maybe<Scalars['Decimal']['output']>;
  lowest52w?: Maybe<Scalars['Decimal']['output']>;
  mainIndex?: Maybe<Scalars['Boolean']['output']>;
  marCat?: Maybe<Scalars['Int']['output']>;
  marketAbbreviation?: Maybe<Scalars['String']['output']>;
  marketCap?: Maybe<Scalars['Decimal']['output']>;
  marketCapEUR?: Maybe<Scalars['Decimal']['output']>;
  marketID: Scalars['Short']['output'];
  marketName?: Maybe<Scalars['String']['output']>;
  marketNumber: Scalars['Short']['output'];
  marketSpecSign?: Maybe<Scalars['String']['output']>;
  marketStatus?: Maybe<Scalars['String']['output']>;
  marketTimeZone?: Maybe<Scalars['String']['output']>;
  mid?: Maybe<Scalars['Decimal']['output']>;
  month?: Maybe<Scalars['Decimal']['output']>;
  netInComeGrowth?: Maybe<Scalars['Float']['output']>;
  netIncome?: Maybe<Scalars['Decimal']['output']>;
  noShares?: Maybe<Scalars['Decimal']['output']>;
  normalDailyClose?: Maybe<Scalars['String']['output']>;
  normalDailyOpen?: Maybe<Scalars['String']['output']>;
  numSharesDate?: Maybe<Scalars['DateTime']['output']>;
  numberOfUnlistedShares?: Maybe<Scalars['Long']['output']>;
  officialClose?: Maybe<Scalars['Decimal']['output']>;
  officialCloseDate?: Maybe<Scalars['Date']['output']>;
  open?: Maybe<Scalars['Decimal']['output']>;
  pE?: Maybe<Scalars['Decimal']['output']>;
  payoutRatio?: Maybe<Scalars['Float']['output']>;
  percent52W?: Maybe<Scalars['Decimal']['output']>;
  prevClose?: Maybe<Scalars['Decimal']['output']>;
  prevMid?: Maybe<Scalars['Decimal']['output']>;
  price2YearsAgo?: Maybe<Scalars['Decimal']['output']>;
  price5YearAgo?: Maybe<Scalars['Decimal']['output']>;
  price52WeekAgo?: Maybe<Scalars['Decimal']['output']>;
  priceBeginOfYear?: Maybe<Scalars['Decimal']['output']>;
  priceMonthAgo?: Maybe<Scalars['Decimal']['output']>;
  priceThreeMonthAgo?: Maybe<Scalars['Decimal']['output']>;
  priceWeekAgo?: Maybe<Scalars['Decimal']['output']>;
  primaryMarket?: Maybe<Scalars['Byte']['output']>;
  regionID?: Maybe<Scalars['Int']['output']>;
  ricCode?: Maybe<Scalars['String']['output']>;
  sPS?: Maybe<Scalars['Float']['output']>;
  shareName: Scalars['String']['output'];
  sixMonthsChange?: Maybe<Scalars['Decimal']['output']>;
  splitRatio?: Maybe<Scalars['Float']['output']>;
  startingDate?: Maybe<Scalars['DateTime']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  tenYearsChange?: Maybe<Scalars['Decimal']['output']>;
  threeMonthChange?: Maybe<Scalars['Decimal']['output']>;
  threeMonthHigh?: Maybe<Scalars['Decimal']['output']>;
  threeMonthLow?: Maybe<Scalars['Decimal']['output']>;
  threeYearsChange?: Maybe<Scalars['Decimal']['output']>;
  ticker: Scalars['String']['output'];
  todayTurnover?: Maybe<Scalars['Decimal']['output']>;
  tokyoEurope?: Maybe<Scalars['Short']['output']>;
  totalMarketCap?: Maybe<Scalars['Decimal']['output']>;
  totalTrades?: Maybe<Scalars['Int']['output']>;
  turnover?: Maybe<Scalars['Decimal']['output']>;
  turnoverGrowth?: Maybe<Scalars['Float']['output']>;
  twoWeek?: Maybe<Scalars['Decimal']['output']>;
  twoYearsChange?: Maybe<Scalars['Decimal']['output']>;
  vWAP?: Maybe<Scalars['Decimal']['output']>;
  visible: Scalars['Boolean']['output'];
  volatility?: Maybe<Scalars['Decimal']['output']>;
  volume?: Maybe<Scalars['Long']['output']>;
  volumeChange?: Maybe<Scalars['Decimal']['output']>;
  volumeTurnover?: Maybe<Scalars['Decimal']['output']>;
  volumeTurnoverUSD?: Maybe<Scalars['Decimal']['output']>;
  wKN?: Maybe<Scalars['String']['output']>;
  week?: Maybe<Scalars['Decimal']['output']>;
  yTD?: Maybe<Scalars['Decimal']['output']>;
  yahooSymbol?: Maybe<Scalars['String']['output']>;
};

export type IntOperationFilterInput = {
  eq?: InputMaybe<Scalars['Int']['input']>;
  gt?: InputMaybe<Scalars['Int']['input']>;
  gte?: InputMaybe<Scalars['Int']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  lt?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['Int']['input']>;
  neq?: InputMaybe<Scalars['Int']['input']>;
  ngt?: InputMaybe<Scalars['Int']['input']>;
  ngte?: InputMaybe<Scalars['Int']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  nlt?: InputMaybe<Scalars['Int']['input']>;
  nlte?: InputMaybe<Scalars['Int']['input']>;
};

export type KeyMetrics = {
  __typename?: 'KeyMetrics';
  averageInventory?: Maybe<Scalars['Float']['output']>;
  averagePayables?: Maybe<Scalars['Float']['output']>;
  averageReceivables?: Maybe<Scalars['Float']['output']>;
  bookValuePerShare?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  capexPerShare?: Maybe<Scalars['Float']['output']>;
  capexToDepreciation?: Maybe<Scalars['Float']['output']>;
  capexToOperatingCashFlow?: Maybe<Scalars['Float']['output']>;
  capexToRevenue?: Maybe<Scalars['Float']['output']>;
  cashPerShare?: Maybe<Scalars['Float']['output']>;
  currentRatio?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  daysOfInventoryOnHand?: Maybe<Scalars['Float']['output']>;
  daysPayablesOutstanding?: Maybe<Scalars['Float']['output']>;
  daysSalesOutstanding?: Maybe<Scalars['Float']['output']>;
  debtToAssets?: Maybe<Scalars['Float']['output']>;
  debtToEquity?: Maybe<Scalars['Float']['output']>;
  dividendYield?: Maybe<Scalars['Float']['output']>;
  earningsYield?: Maybe<Scalars['Float']['output']>;
  enterpriseValue?: Maybe<Scalars['Float']['output']>;
  enterpriseValueOverEBITDA?: Maybe<Scalars['Float']['output']>;
  evToFreeCashFlow?: Maybe<Scalars['Float']['output']>;
  evToOperatingCashFlow?: Maybe<Scalars['Float']['output']>;
  evToSales?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['String']['output']>;
  freeCashFlowPerShare?: Maybe<Scalars['Float']['output']>;
  freeCashFlowYield?: Maybe<Scalars['Float']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  grahamNetNet?: Maybe<Scalars['Float']['output']>;
  grahamNumber?: Maybe<Scalars['Float']['output']>;
  incomeQuality?: Maybe<Scalars['Float']['output']>;
  intangiblesToTotalAssets?: Maybe<Scalars['Float']['output']>;
  interestCoverage?: Maybe<Scalars['Float']['output']>;
  interestDebtPerShare?: Maybe<Scalars['Float']['output']>;
  inventoryTurnover?: Maybe<Scalars['Float']['output']>;
  investedCapital?: Maybe<Scalars['Float']['output']>;
  marketCap?: Maybe<Scalars['Float']['output']>;
  netCurrentAssetValue?: Maybe<Scalars['Float']['output']>;
  netDebtToEBITDA?: Maybe<Scalars['Float']['output']>;
  netIncomePerShare?: Maybe<Scalars['Float']['output']>;
  operatingCashFlowPerShare?: Maybe<Scalars['Float']['output']>;
  payablesTurnover?: Maybe<Scalars['Float']['output']>;
  payoutRatio?: Maybe<Scalars['Float']['output']>;
  pbRatio?: Maybe<Scalars['Float']['output']>;
  peRatio?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  pfcfRatio?: Maybe<Scalars['Float']['output']>;
  pocfratio?: Maybe<Scalars['Float']['output']>;
  priceToSalesRatio?: Maybe<Scalars['Float']['output']>;
  ptbRatio?: Maybe<Scalars['Float']['output']>;
  receivablesTurnover?: Maybe<Scalars['Float']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  researchAndDdevelopementToRevenue?: Maybe<Scalars['Float']['output']>;
  returnOnTangibleAssets?: Maybe<Scalars['Float']['output']>;
  revenuePerShare?: Maybe<Scalars['Float']['output']>;
  roe?: Maybe<Scalars['Float']['output']>;
  roic?: Maybe<Scalars['Float']['output']>;
  salesGeneralAndAdministrativeToRevenue?: Maybe<Scalars['Float']['output']>;
  shareholdersEquityPerShare?: Maybe<Scalars['Float']['output']>;
  stockBasedCompensationToRevenue?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  tangibleAssetValue?: Maybe<Scalars['Float']['output']>;
  tangibleBookValuePerShare?: Maybe<Scalars['Float']['output']>;
  workingCapital?: Maybe<Scalars['Float']['output']>;
};

/** A segment of a collection. */
export type KeyMetricsCollectionSegment = {
  __typename?: 'KeyMetricsCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<KeyMetrics>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type KeyMetricsTtm = {
  __typename?: 'KeyMetricsTTM';
  averageInventoryTTM?: Maybe<Scalars['Float']['output']>;
  averagePayablesTTM?: Maybe<Scalars['Float']['output']>;
  averageReceivablesTTM?: Maybe<Scalars['Float']['output']>;
  bookValuePerShareTTM?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  capexPerShareTTM?: Maybe<Scalars['Float']['output']>;
  capexToDepreciationTTM?: Maybe<Scalars['Float']['output']>;
  capexToOperatingCashFlowTTM?: Maybe<Scalars['Float']['output']>;
  capexToRevenueTTM?: Maybe<Scalars['Float']['output']>;
  cashPerShareTTM?: Maybe<Scalars['Float']['output']>;
  currentRatioTTM?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  daysOfInventoryOnHandTTM?: Maybe<Scalars['Float']['output']>;
  daysPayablesOutstandingTTM?: Maybe<Scalars['Float']['output']>;
  daysSalesOutstandingTTM?: Maybe<Scalars['Float']['output']>;
  debtToAssetsTTM?: Maybe<Scalars['Float']['output']>;
  debtToEquityTTM?: Maybe<Scalars['Float']['output']>;
  debtToMarketCapTTM?: Maybe<Scalars['Float']['output']>;
  dividendPerShareTTM?: Maybe<Scalars['Float']['output']>;
  dividendYieldPercentageTTM?: Maybe<Scalars['Float']['output']>;
  dividendYieldTTM?: Maybe<Scalars['Float']['output']>;
  earningsYieldTTM?: Maybe<Scalars['Float']['output']>;
  enterpriseValueOverEBITDATTM?: Maybe<Scalars['Float']['output']>;
  enterpriseValueTTM?: Maybe<Scalars['Float']['output']>;
  evToFreeCashFlowTTM?: Maybe<Scalars['Float']['output']>;
  evToOperatingCashFlowTTM?: Maybe<Scalars['Float']['output']>;
  evToSalesTTM?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['Date']['output']>;
  freeCashFlowPerShareTTM?: Maybe<Scalars['Float']['output']>;
  freeCashFlowYieldTTM?: Maybe<Scalars['Float']['output']>;
  grahamNetNetTTM?: Maybe<Scalars['Float']['output']>;
  grahamNumberTTM?: Maybe<Scalars['Float']['output']>;
  incomeQualityTTM?: Maybe<Scalars['Float']['output']>;
  intangiblesToTotalAssetsTTM?: Maybe<Scalars['Float']['output']>;
  interestCoverageTTM?: Maybe<Scalars['Float']['output']>;
  interestDebtPerShareTTM?: Maybe<Scalars['Float']['output']>;
  inventoryTurnoverTTM?: Maybe<Scalars['Float']['output']>;
  investedCapitalTTM?: Maybe<Scalars['Float']['output']>;
  marketCapTTM?: Maybe<Scalars['Float']['output']>;
  netCurrentAssetValueTTM?: Maybe<Scalars['Float']['output']>;
  netDebtToEBITDATTM?: Maybe<Scalars['Float']['output']>;
  netIncomePerShareTTM?: Maybe<Scalars['Float']['output']>;
  operatingCashFlowPerShareTTM?: Maybe<Scalars['Float']['output']>;
  payablesTurnoverTTM?: Maybe<Scalars['Float']['output']>;
  payoutRatioTTM?: Maybe<Scalars['Float']['output']>;
  pbRatioTTM?: Maybe<Scalars['Float']['output']>;
  peRatioTTM?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  pfcfRatioTTM?: Maybe<Scalars['Float']['output']>;
  pocfratioTTM?: Maybe<Scalars['Float']['output']>;
  priceToSalesRatioTTM?: Maybe<Scalars['Float']['output']>;
  ptbRatioTTM?: Maybe<Scalars['Float']['output']>;
  receivablesTurnoverTTM?: Maybe<Scalars['Float']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  researchAndDevelopementToRevenueTTM?: Maybe<Scalars['Float']['output']>;
  returnOnTangibleAssetsTTM?: Maybe<Scalars['Float']['output']>;
  revenuePerShareTTM?: Maybe<Scalars['Float']['output']>;
  roeTTM?: Maybe<Scalars['Float']['output']>;
  roicTTM?: Maybe<Scalars['Float']['output']>;
  salesGeneralAndAdministrativeToRevenueTTM?: Maybe<Scalars['Float']['output']>;
  shareholdersEquityPerShareTTM?: Maybe<Scalars['Float']['output']>;
  stockBasedCompensationToRevenueTTM?: Maybe<Scalars['Float']['output']>;
  tangibleAssetValueTTM?: Maybe<Scalars['Float']['output']>;
  tangibleBookValuePerShareTTM?: Maybe<Scalars['Float']['output']>;
  workingCapitalTTM?: Maybe<Scalars['Float']['output']>;
};

/** A segment of a collection. */
export type KeyMetricsTtmCollectionSegment = {
  __typename?: 'KeyMetricsTTMCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<KeyMetricsTtm>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type Language = {
  __typename?: 'Language';
  cultureCode: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  nameEnglish: Scalars['String']['output'];
};

export type LanguageDtoFilterInput = {
  and?: InputMaybe<Array<LanguageDtoFilterInput>>;
  cultureCode?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<IntOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  nameEnglish?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<LanguageDtoFilterInput>>;
};

export type LatestShareTradesDto_Deprecated = {
  __typename?: 'LatestShareTradesDto_Deprecated';
  close: Scalars['Decimal']['output'];
  date: Scalars['DateTime']['output'];
  size?: Maybe<Scalars['Long']['output']>;
};

export type List = {
  __typename?: 'List';
  arabic?: Maybe<Scalars['String']['output']>;
  chinese?: Maybe<Scalars['String']['output']>;
  danish?: Maybe<Scalars['String']['output']>;
  dutch?: Maybe<Scalars['String']['output']>;
  finnish?: Maybe<Scalars['String']['output']>;
  french?: Maybe<Scalars['String']['output']>;
  german?: Maybe<Scalars['String']['output']>;
  icelandic?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  italian?: Maybe<Scalars['String']['output']>;
  japanese?: Maybe<Scalars['String']['output']>;
  korean?: Maybe<Scalars['String']['output']>;
  listName: Scalars['String']['output'];
  listNameByCurrentCulture?: Maybe<Scalars['String']['output']>;
  marketID?: Maybe<Scalars['Byte']['output']>;
  norwegian?: Maybe<Scalars['String']['output']>;
  polish?: Maybe<Scalars['String']['output']>;
  russian?: Maybe<Scalars['String']['output']>;
  spanish?: Maybe<Scalars['String']['output']>;
  swedish?: Maybe<Scalars['String']['output']>;
  taiwanese?: Maybe<Scalars['String']['output']>;
  vietnamese?: Maybe<Scalars['String']['output']>;
};

export type ListFilterInputTypeOfWebcastVideoDtoFilterInput = {
  all?: InputMaybe<WebcastVideoDtoFilterInput>;
  any?: InputMaybe<Scalars['Boolean']['input']>;
  none?: InputMaybe<WebcastVideoDtoFilterInput>;
  some?: InputMaybe<WebcastVideoDtoFilterInput>;
};

export type LongOperationFilterInput = {
  eq?: InputMaybe<Scalars['Long']['input']>;
  gt?: InputMaybe<Scalars['Long']['input']>;
  gte?: InputMaybe<Scalars['Long']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Long']['input']>>>;
  lt?: InputMaybe<Scalars['Long']['input']>;
  lte?: InputMaybe<Scalars['Long']['input']>;
  neq?: InputMaybe<Scalars['Long']['input']>;
  ngt?: InputMaybe<Scalars['Long']['input']>;
  ngte?: InputMaybe<Scalars['Long']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['Long']['input']>>>;
  nlt?: InputMaybe<Scalars['Long']['input']>;
  nlte?: InputMaybe<Scalars['Long']['input']>;
};

export type Market = HasTranslation & {
  __typename?: 'Market';
  abbreviation?: Maybe<Scalars['String']['output']>;
  businessDaysStoT: Scalars['Boolean']['output'];
  city?: Maybe<City>;
  cityId: Scalars['Int']['output'];
  closeTimeLocal?: Maybe<Scalars['String']['output']>;
  delay?: Maybe<Scalars['String']['output']>;
  id: Scalars['Short']['output'];
  openTimeLocal?: Maybe<Scalars['String']['output']>;
  parentId?: Maybe<Scalars['Short']['output']>;
  regionId?: Maybe<Scalars['Int']['output']>;
  status?: Maybe<MarketStatus>;
  timeDiff: Scalars['Short']['output'];
  timezone?: Maybe<Timezone>;
  timezoneId: Scalars['Int']['output'];
  timezoneName: Scalars['String']['output'];
  translation?: Maybe<Translation>;
  translationId?: Maybe<Scalars['Int']['output']>;
};


export type MarketTranslationArgs = {
  cultureName?: InputMaybe<Scalars['String']['input']>;
};

export type MarketDepth = {
  __typename?: 'MarketDepth';
  buyPrice: Scalars['Decimal']['output'];
  buyVolume: Scalars['Int']['output'];
  sellPrice: Scalars['Decimal']['output'];
  sellVolume: Scalars['Int']['output'];
};

export type MarketOrder_Deprecated = {
  __typename?: 'MarketOrder_Deprecated';
  buyPrice: Scalars['Decimal']['output'];
  buyVolume: Scalars['Int']['output'];
  sellPrice: Scalars['Decimal']['output'];
  sellVolume: Scalars['Int']['output'];
};

/** Represents the current status of a Market */
export type MarketStatus = {
  __typename?: 'MarketStatus';
  /** Indicates that market is currently openning */
  isOpened: Scalars['Boolean']['output'];
  /** Remaining time (in minutes) to let the market is going to be opened/closed. */
  remainingTime: Scalars['Float']['output'];
};

export type MessageTypeDtoFilterInput = {
  and?: InputMaybe<Array<MessageTypeDtoFilterInput>>;
  id?: InputMaybe<IntOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<MessageTypeDtoFilterInput>>;
  shortName?: InputMaybe<StringOperationFilterInput>;
};

export type MessageType_Deprecated = {
  __typename?: 'MessageType_Deprecated';
  id?: Maybe<Scalars['Int']['output']>;
  messageGroupId?: Maybe<Scalars['Int']['output']>;
  messageGroupTypeId?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  shortName?: Maybe<Scalars['String']['output']>;
  sourceId: Scalars['Int']['output'];
};

export type OrderDepth = {
  __typename?: 'OrderDepth';
  instrument: Instrument;
  instrumentId: Scalars['Int']['output'];
  marketDepths: Array<MarketDepth>;
  rowUpdated: Scalars['DateTime']['output'];
};

export type OrderDepth_Deprecated = {
  __typename?: 'OrderDepth_Deprecated';
  marketDepth?: Maybe<Array<MarketOrder_Deprecated>>;
  rowUpdated: Scalars['DateTime']['output'];
};

export type OwnerEarnings = {
  __typename?: 'OwnerEarnings';
  averagePPE?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  fillingDate?: Maybe<Scalars['Date']['output']>;
  growthCapex?: Maybe<Scalars['Float']['output']>;
  maintenanceCapex?: Maybe<Scalars['Float']['output']>;
  ownersEarnings?: Maybe<Scalars['Float']['output']>;
  ownersEarningsPerShare?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
};

/** A segment of a collection. */
export type OwnerEarningsCollectionSegment = {
  __typename?: 'OwnerEarningsCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<OwnerEarnings>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

/** Information about pagination in a connection. */
export type PageInfo = {
  __typename?: 'PageInfo';
  /** When paginating forwards, the cursor to continue. */
  endCursor?: Maybe<Scalars['String']['output']>;
  /** Indicates whether more edges exist following the set defined by the clients arguments. */
  hasNextPage: Scalars['Boolean']['output'];
  /** Indicates whether more edges exist prior the set defined by the clients arguments. */
  hasPreviousPage: Scalars['Boolean']['output'];
  /** When paginating backwards, the cursor to continue. */
  startCursor?: Maybe<Scalars['String']['output']>;
};

export enum PerformancePeriod {
  /** All time of prices */
  AllTime = 'ALL_TIME',
  /** Custom with any input date */
  Custom = 'CUSTOM',
  /** Fifty two weeks ago */
  FiftyTwoWeeks = 'FIFTY_TWO_WEEKS',
  /** Five years ago */
  FiveYears = 'FIVE_YEARS',
  /** One month ago */
  OneMonth = 'ONE_MONTH',
  /** One week ago */
  OneWeek = 'ONE_WEEK',
  /** One year ago */
  OneYear = 'ONE_YEAR',
  /** Six months ago */
  SixMonths = 'SIX_MONTHS',
  /** Ten years ago */
  TenYears = 'TEN_YEARS',
  /** Three months ago */
  ThreeMonths = 'THREE_MONTHS',
  /** Three years ago */
  ThreeYears = 'THREE_YEARS',
  /** Two weeks ago */
  TwoWeeks = 'TWO_WEEKS',
  /** Two years ago */
  TwoYears = 'TWO_YEARS',
  /** Year to date */
  Ytd = 'YTD'
}

export type PerformanceShare = {
  __typename?: 'PerformanceShare';
  changePercentage?: Maybe<Scalars['Decimal']['output']>;
  closePrice?: Maybe<Scalars['Decimal']['output']>;
  closeRate?: Maybe<Scalars['Decimal']['output']>;
  highest?: Maybe<Scalars['Decimal']['output']>;
  highestDate?: Maybe<Scalars['DateTime']['output']>;
  instrumentId: Scalars['Int']['output'];
  lowest?: Maybe<Scalars['Decimal']['output']>;
  lowestDate?: Maybe<Scalars['DateTime']['output']>;
};

export enum Period {
  FiveMinute = 'FIVE_MINUTE',
  OneHour = 'ONE_HOUR',
  OneMinute = 'ONE_MINUTE',
  TenMinute = 'TEN_MINUTE'
}

export enum Periods {
  All = 'ALL',
  CustomRange = 'CUSTOM_RANGE',
  FiveYears = 'FIVE_YEARS',
  OneMonth = 'ONE_MONTH',
  OneYear = 'ONE_YEAR',
  SixMonths = 'SIX_MONTHS',
  ThreeMonths = 'THREE_MONTHS',
  ThreeYears = 'THREE_YEARS',
  Ytd = 'YTD'
}

export type PressRelease = {
  __typename?: 'PressRelease';
  /**
   * List of all attachments relating to a pressrelease.
   *         This field may has issue about the loading performance if there are a huge
   *         attachments need to query at same time. Use `@files` field to have better performance.
   */
  attachments?: Maybe<AttachmentConnection>;
  company?: Maybe<Company>;
  companyCode: Scalars['String']['output'];
  /** @deprecated Use field "DateTime" instead. This deprecated field will be remove in next release */
  date?: Maybe<Scalars['String']['output']>;
  /** Published date of the release at the stock exchange timezone, but not UTC although it is in ISO format of UTC. */
  dateTime: Scalars['DateTime']['output'];
  /** List of all attachments relating to a pressrelease. */
  files: Array<Attachment>;
  hasAttachment: Scalars['Boolean']['output'];
  id: Scalars['Long']['output'];
  insertedDate: Scalars['DateTime']['output'];
  isHidden?: Maybe<Scalars['Boolean']['output']>;
  language?: Maybe<Language>;
  languageId: Scalars['Int']['output'];
  message?: Maybe<Scalars['String']['output']>;
  messageType?: Maybe<PressReleaseMessageType>;
  messageTypeId?: Maybe<Scalars['Int']['output']>;
  sourceId: Scalars['Int']['output'];
  title?: Maybe<Scalars['String']['output']>;
};


export type PressReleaseAttachmentsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<AttachmentDtoSortInput>>;
  where?: InputMaybe<AttachmentDtoFilterInput>;
};

export type PressReleaseAvailableYearRangeDto = {
  __typename?: 'PressReleaseAvailableYearRangeDto';
  fromYear: Scalars['Int']['output'];
  toYear: Scalars['Int']['output'];
};

export type PressReleaseDtoFilterInput = {
  and?: InputMaybe<Array<PressReleaseDtoFilterInput>>;
  companyCode?: InputMaybe<StringOperationFilterInput>;
  date?: InputMaybe<StringOperationFilterInput>;
  dateTime?: InputMaybe<DateTimeOperationFilterInput>;
  hasAttachment?: InputMaybe<BooleanOperationFilterInput>;
  id?: InputMaybe<LongOperationFilterInput>;
  insertedDate?: InputMaybe<DateTimeOperationFilterInput>;
  isHidden?: InputMaybe<BooleanOperationFilterInput>;
  languageId?: InputMaybe<IntOperationFilterInput>;
  messageType?: InputMaybe<MessageTypeDtoFilterInput>;
  messageTypeId?: InputMaybe<IntOperationFilterInput>;
  or?: InputMaybe<Array<PressReleaseDtoFilterInput>>;
  sourceId?: InputMaybe<IntOperationFilterInput>;
  title?: InputMaybe<StringOperationFilterInput>;
};

export type PressReleaseDtoSortInput = {
  dateTime?: InputMaybe<SortEnumType>;
  insertedDate?: InputMaybe<SortEnumType>;
};

export type PressReleaseMessageType = {
  __typename?: 'PressReleaseMessageType';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  shortName?: Maybe<Scalars['String']['output']>;
};

export type PressRelease_Deprecated = {
  __typename?: 'PressRelease_Deprecated';
  attachment: Array<Attachment_Deprecated>;
  companyCode: Scalars['String']['output'];
  date: Scalars['String']['output'];
  dateTime: Scalars['DateTime']['output'];
  hasAttachment: Scalars['Boolean']['output'];
  id: Scalars['Long']['output'];
  insertedDate: Scalars['DateTime']['output'];
  isHidden?: Maybe<Scalars['Boolean']['output']>;
  languageId: Scalars['Int']['output'];
  message: Scalars['String']['output'];
  messageGroupId?: Maybe<Scalars['Int']['output']>;
  messageType?: Maybe<MessageType_Deprecated>;
  messageTypeId?: Maybe<Scalars['Int']['output']>;
  sourceId: Scalars['Int']['output'];
  title: Scalars['String']['output'];
};

/** A connection to a list of items. */
export type PressReleasesConnection = {
  __typename?: 'PressReleasesConnection';
  /** A list of edges. */
  edges?: Maybe<Array<PressReleasesEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<PressRelease>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type PressReleasesEdge = {
  __typename?: 'PressReleasesEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: PressRelease;
};

export type Query = {
  __typename?: 'Query';
  company?: Maybe<Company>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  currency: CurrencyQueries_Deprecated;
  currencyByCode?: Maybe<Currency>;
  currencyRate?: Maybe<CurrencyRate>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  dividendEvents: Array<DividendEvent_Deprecated>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  earningsEvents: Array<EarningEvent_Deprecated>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  historicalData?: Maybe<Array<StockData_Deprecated>>;
  instrumentById?: Maybe<Instrument>;
  instrumentByIds?: Maybe<Array<Maybe<Instrument>>>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  instrumentHistorical: Array<InstrumentHistory_Deprecated_>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  instrumentPerformance: Array<InstrumentYearlyPerformancePrice_Deprecated>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  instruments: Array<Instrument_Deprecated>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  intradayData: Array<InstrumentDailyData_Deprecated>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  latestShareTrades: Array<LatestShareTradesDto_Deprecated>;
  /** @deprecated This field will be removed in the next release. Use instrument->orderDepth instead. */
  orderDepths: OrderDepth_Deprecated;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  pressReleaseDetail: Array<PressRelease_Deprecated>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  pressReleases: Array<PressRelease_Deprecated>;
  pressrelease?: Maybe<PressRelease>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  stockOverview?: Maybe<StockData_Deprecated>;
  /** List all languages that supported by Euroland system */
  systemLanguages: Array<Language>;
  /** @deprecated No longer maintenance and will be removed in the next release. */
  todayShareTrades: Array<LatestShareTradesDto_Deprecated>;
  /** @deprecated This field may be removed in the next releas. Use company->webcast instead. */
  webcast: WebcastDetail_Deprecated;
  /** @deprecated This field may be removed in the next releas. Use company->webcasts instead. */
  webcasts: Array<WebcastDetail_Deprecated>;
};


export type QueryCompanyArgs = {
  code: Scalars['String']['input'];
};


export type QueryCurrencyByCodeArgs = {
  currencyCode: Scalars['String']['input'];
};


export type QueryCurrencyRateArgs = {
  baseCurrency: Scalars['String']['input'];
  quoteCurrency: Scalars['String']['input'];
};


export type QueryDividendEventsArgs = {
  fromDate?: InputMaybe<Scalars['DateTime']['input']>;
  instrumentId: Scalars['Int']['input'];
  isRT?: Scalars['Boolean']['input'];
  toDate?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryEarningsEventsArgs = {
  companyCode: Scalars['String']['input'];
  fromDate?: InputMaybe<Scalars['DateTime']['input']>;
  isRT?: Scalars['Boolean']['input'];
  toDate?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryHistoricalDataArgs = {
  from?: InputMaybe<Scalars['DateTime']['input']>;
  indices: Array<Scalars['Int']['input']>;
  instrumentId: Scalars['Int']['input'];
  isRT?: Scalars['Boolean']['input'];
  mas: Array<Scalars['Int']['input']>;
  peers: Array<Scalars['Int']['input']>;
  period?: Periods;
  to?: InputMaybe<Scalars['DateTime']['input']>;
  toCurrency?: InputMaybe<Scalars['String']['input']>;
  type?: Historical;
};


export type QueryInstrumentByIdArgs = {
  adjClose?: InputMaybe<Scalars['Boolean']['input']>;
  exchangeCurrency?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
};


export type QueryInstrumentByIdsArgs = {
  adjClose?: InputMaybe<Scalars['Boolean']['input']>;
  exchangeCurrency?: InputMaybe<Scalars['String']['input']>;
  ids: Array<Scalars['Int']['input']>;
};


export type QueryInstrumentHistoricalArgs = {
  fromDate?: InputMaybe<Scalars['DateTime']['input']>;
  instrumentId: Scalars['Int']['input'];
  isIntraday: Scalars['Boolean']['input'];
  isRT?: Scalars['Boolean']['input'];
  period?: Period;
  toCurrency?: InputMaybe<Scalars['String']['input']>;
  toDate?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryInstrumentPerformanceArgs = {
  instrumentIds: Array<Scalars['Int']['input']>;
  numberOfYears?: Scalars['Int']['input'];
  toCurrency?: InputMaybe<Scalars['String']['input']>;
};


export type QueryInstrumentsArgs = {
  dayPeriod?: Scalars['Int']['input'];
  instrumentIds: Array<Scalars['Int']['input']>;
  isRT?: Scalars['Boolean']['input'];
  toCurrency?: InputMaybe<Scalars['String']['input']>;
};


export type QueryIntradayDataArgs = {
  instrumentId: Scalars['Int']['input'];
  isRT?: Scalars['Boolean']['input'];
  toCurrency?: InputMaybe<Scalars['String']['input']>;
};


export type QueryLatestShareTradesArgs = {
  count?: Scalars['Int']['input'];
  instrumentId: Scalars['Int']['input'];
  isRT?: Scalars['Boolean']['input'];
  toCurrency?: InputMaybe<Scalars['String']['input']>;
};


export type QueryOrderDepthsArgs = {
  instrumentId: Scalars['Int']['input'];
  isRT?: Scalars['Boolean']['input'];
  toCurrency?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPressReleaseDetailArgs = {
  prId: Scalars['Long']['input'];
};


export type QueryPressReleasesArgs = {
  companyCode: Scalars['String']['input'];
  excludeMessageTypeIDs?: InputMaybe<Scalars['String']['input']>;
  fromDate?: InputMaybe<Scalars['DateTime']['input']>;
  includeMessageTypeIDs?: InputMaybe<Scalars['String']['input']>;
  languageForwarding?: InputMaybe<Scalars['String']['input']>;
  prSources?: InputMaybe<Scalars['String']['input']>;
  toDate?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryPressreleaseArgs = {
  groupByMessageType?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['Long']['input'];
  includeHidden?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryStockOverviewArgs = {
  from?: InputMaybe<Scalars['DateTime']['input']>;
  instrumentId: Scalars['Int']['input'];
  period?: Periods;
  to?: InputMaybe<Scalars['DateTime']['input']>;
  toCurrency?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySystemLanguagesArgs = {
  where?: InputMaybe<LanguageDtoFilterInput>;
};


export type QueryTodayShareTradesArgs = {
  instrumentId: Scalars['Int']['input'];
  isRT?: Scalars['Boolean']['input'];
  toCurrency?: InputMaybe<Scalars['String']['input']>;
};


export type QueryWebcastArgs = {
  id: Scalars['Long']['input'];
};


export type QueryWebcastsArgs = {
  companyCode: Scalars['String']['input'];
  from: Scalars['DateTime']['input'];
  to?: InputMaybe<Scalars['DateTime']['input']>;
};

export type Ratios = {
  __typename?: 'Ratios';
  assetTurnover?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  capitalExpenditureCoverageRatio?: Maybe<Scalars['Float']['output']>;
  cashConversionCycle?: Maybe<Scalars['String']['output']>;
  cashFlowCoverageRatios?: Maybe<Scalars['Float']['output']>;
  cashFlowToDebtRatio?: Maybe<Scalars['Float']['output']>;
  cashPerShare?: Maybe<Scalars['Float']['output']>;
  cashRatio?: Maybe<Scalars['Float']['output']>;
  companyEquityMultiplier?: Maybe<Scalars['Float']['output']>;
  currentRatio?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  daysOfInventoryOutstanding?: Maybe<Scalars['Float']['output']>;
  daysOfPayablesOutstanding?: Maybe<Scalars['Float']['output']>;
  daysOfSalesOutstanding?: Maybe<Scalars['Float']['output']>;
  debtEquityRatio?: Maybe<Scalars['Float']['output']>;
  debtRatio?: Maybe<Scalars['Float']['output']>;
  dividendPaidAndCapexCoverageRatio?: Maybe<Scalars['Float']['output']>;
  dividendPayoutRatio?: Maybe<Scalars['Float']['output']>;
  dividendYield?: Maybe<Scalars['Float']['output']>;
  ebitPerRevenue?: Maybe<Scalars['Float']['output']>;
  ebtPerEbit?: Maybe<Scalars['Float']['output']>;
  effectiveTaxRate?: Maybe<Scalars['Float']['output']>;
  enterpriseValueMultiple?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['String']['output']>;
  fixedAssetTurnover?: Maybe<Scalars['Float']['output']>;
  freeCashFlowOperatingCashFlowRatio?: Maybe<Scalars['Float']['output']>;
  freeCashFlowPerShare?: Maybe<Scalars['Float']['output']>;
  fye?: Maybe<Scalars['Date']['output']>;
  fys?: Maybe<Scalars['Date']['output']>;
  grossProfitMargin?: Maybe<Scalars['Float']['output']>;
  interestCoverage?: Maybe<Scalars['Float']['output']>;
  inventoryTurnover?: Maybe<Scalars['Float']['output']>;
  longTermDebtToCapitalization?: Maybe<Scalars['Float']['output']>;
  netIncomePerEBT?: Maybe<Scalars['Float']['output']>;
  netProfitMargin?: Maybe<Scalars['Float']['output']>;
  operatingCashFlowPerShare?: Maybe<Scalars['Float']['output']>;
  operatingCashFlowSalesRatio?: Maybe<Scalars['Float']['output']>;
  operatingCycle?: Maybe<Scalars['String']['output']>;
  operatingProfitMargin?: Maybe<Scalars['Float']['output']>;
  payablesTurnover?: Maybe<Scalars['Float']['output']>;
  payoutRatio?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['Date']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['Date']['output']>;
  periodStartMonth?: Maybe<Scalars['String']['output']>;
  pretaxProfitMargin?: Maybe<Scalars['Float']['output']>;
  priceBookValueRatio?: Maybe<Scalars['Float']['output']>;
  priceCashFlowRatio?: Maybe<Scalars['Float']['output']>;
  priceEarningsRatio?: Maybe<Scalars['Float']['output']>;
  priceEarningsToGrowthRatio?: Maybe<Scalars['Float']['output']>;
  priceFairValue?: Maybe<Scalars['Float']['output']>;
  priceSalesRatio?: Maybe<Scalars['Float']['output']>;
  priceToBookRatio?: Maybe<Scalars['Float']['output']>;
  priceToFreeCashFlowsRatio?: Maybe<Scalars['Float']['output']>;
  priceToOperatingCashFlowsRatio?: Maybe<Scalars['Float']['output']>;
  priceToSalesRatio?: Maybe<Scalars['Float']['output']>;
  quickRatio?: Maybe<Scalars['Float']['output']>;
  receivablesTurnover?: Maybe<Scalars['Float']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  returnOnAssets?: Maybe<Scalars['Float']['output']>;
  returnOnCapitalEmployed?: Maybe<Scalars['Float']['output']>;
  returnOnEquity?: Maybe<Scalars['Float']['output']>;
  shortTermCoverageRatios?: Maybe<Scalars['Float']['output']>;
  symbol?: Maybe<Scalars['String']['output']>;
  totalDebtToCapitalization?: Maybe<Scalars['Float']['output']>;
};

/** A segment of a collection. */
export type RatiosCollectionSegment = {
  __typename?: 'RatiosCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<Ratios>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type RatiosTtm = {
  __typename?: 'RatiosTTM';
  assetTurnoverTTM?: Maybe<Scalars['Float']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  capitalExpenditureCoverageRatioTTM?: Maybe<Scalars['Float']['output']>;
  cashConversionCycleTTM?: Maybe<Scalars['Float']['output']>;
  cashFlowCoverageRatiosTTM?: Maybe<Scalars['Float']['output']>;
  cashFlowToDebtRatioTTM?: Maybe<Scalars['Float']['output']>;
  cashPerShareTTM?: Maybe<Scalars['Float']['output']>;
  cashRatioTTM?: Maybe<Scalars['Float']['output']>;
  companyEquityMultiplierTTM?: Maybe<Scalars['Float']['output']>;
  currentRatioTTM?: Maybe<Scalars['Float']['output']>;
  date?: Maybe<Scalars['Date']['output']>;
  daysOfInventoryOutstandingTTM?: Maybe<Scalars['Float']['output']>;
  daysOfPayablesOutstandingTTM?: Maybe<Scalars['Float']['output']>;
  daysOfSalesOutstandingTTM?: Maybe<Scalars['Float']['output']>;
  debtEquityRatioTTM?: Maybe<Scalars['Float']['output']>;
  debtRatioTTM?: Maybe<Scalars['Float']['output']>;
  dividendPaidAndCapexCoverageRatioTTM?: Maybe<Scalars['Float']['output']>;
  dividendPerShareTTM?: Maybe<Scalars['Float']['output']>;
  dividendYielPercentageTTM?: Maybe<Scalars['Float']['output']>;
  dividendYielTTM?: Maybe<Scalars['Float']['output']>;
  ebitPerRevenueTTM?: Maybe<Scalars['Float']['output']>;
  ebtPerEbitTTM?: Maybe<Scalars['Float']['output']>;
  effectiveTaxRateTTM?: Maybe<Scalars['Float']['output']>;
  enterpriseValueMultipleTTM?: Maybe<Scalars['Float']['output']>;
  fillingDate?: Maybe<Scalars['Date']['output']>;
  fixedAssetTurnoverTTM?: Maybe<Scalars['Float']['output']>;
  freeCashFlowOperatingCashFlowRatioTTM?: Maybe<Scalars['Float']['output']>;
  freeCashFlowPerShareTTM?: Maybe<Scalars['Float']['output']>;
  grossProfitMarginTTM?: Maybe<Scalars['Float']['output']>;
  interestCoverageTTM?: Maybe<Scalars['Float']['output']>;
  inventoryTurnoverTTM?: Maybe<Scalars['Float']['output']>;
  longTermDebtToCapitalizationTTM?: Maybe<Scalars['Float']['output']>;
  netIncomePerEBTTTM?: Maybe<Scalars['Float']['output']>;
  netProfitMarginTTM?: Maybe<Scalars['Float']['output']>;
  operatingCashFlowPerShareTTM?: Maybe<Scalars['Float']['output']>;
  operatingCashFlowSalesRatioTTM?: Maybe<Scalars['Float']['output']>;
  operatingCycleTTM?: Maybe<Scalars['Float']['output']>;
  operatingProfitMarginTTM?: Maybe<Scalars['Float']['output']>;
  payablesTurnoverTTM?: Maybe<Scalars['Float']['output']>;
  payoutRatioTTM?: Maybe<Scalars['Float']['output']>;
  peRatioTTM?: Maybe<Scalars['Float']['output']>;
  pegRatioTTM?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  pretaxProfitMarginTTM?: Maybe<Scalars['Float']['output']>;
  priceBookValueRatioTTM?: Maybe<Scalars['Float']['output']>;
  priceCashFlowRatioTTM?: Maybe<Scalars['Float']['output']>;
  priceEarningsRatioTTM?: Maybe<Scalars['Float']['output']>;
  priceEarningsToGrowthRatioTTM?: Maybe<Scalars['Float']['output']>;
  priceFairValueTTM?: Maybe<Scalars['Float']['output']>;
  priceSalesRatioTTM?: Maybe<Scalars['Float']['output']>;
  priceToBookRatioTTM?: Maybe<Scalars['Float']['output']>;
  priceToFreeCashFlowsRatioTTM?: Maybe<Scalars['Float']['output']>;
  priceToOperatingCashFlowsRatioTTM?: Maybe<Scalars['Float']['output']>;
  priceToSalesRatioTTM?: Maybe<Scalars['Float']['output']>;
  quickRatioTTM?: Maybe<Scalars['Float']['output']>;
  receivablesTurnoverTTM?: Maybe<Scalars['Float']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  returnOnAssetsTTM?: Maybe<Scalars['Float']['output']>;
  returnOnCapitalEmployedTTM?: Maybe<Scalars['Float']['output']>;
  returnOnEquityTTM?: Maybe<Scalars['Float']['output']>;
  shortTermCoverageRatiosTTM?: Maybe<Scalars['Float']['output']>;
  totalDebtToCapitalizationTTM?: Maybe<Scalars['Float']['output']>;
};

/** A segment of a collection. */
export type RatiosTtmCollectionSegment = {
  __typename?: 'RatiosTTMCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<RatiosTtm>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type Report = {
  __typename?: 'Report';
  fileLocation?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  languageCode: Scalars['String']['output'];
  languageName?: Maybe<Scalars['String']['output']>;
  reportParentTypeDisplayName: Scalars['String']['output'];
  reportParentTypeId: Scalars['Int']['output'];
  reportParentTypeOrder: Scalars['Int']['output'];
  reportTypeId: Scalars['Int']['output'];
  reportTypeName: Scalars['String']['output'];
  reportTypeOrder: Scalars['Int']['output'];
  subTitle?: Maybe<Scalars['String']['output']>;
  thumbnailFileLocation: Scalars['String']['output'];
  thumbnailFileLocationFull: Scalars['String']['output'];
  title: Scalars['String']['output'];
  uploadedDate: Scalars['DateTime']['output'];
  uploadedDateUTC: Scalars['DateTime']['output'];
  year: Scalars['Int']['output'];
};

export type ReportDtoFilterInput = {
  and?: InputMaybe<Array<ReportDtoFilterInput>>;
  fileLocation?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<LongOperationFilterInput>;
  languageCode?: InputMaybe<StringOperationFilterInput>;
  languageName?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<ReportDtoFilterInput>>;
  reportParentTypeDisplayName?: InputMaybe<StringOperationFilterInput>;
  reportParentTypeId?: InputMaybe<IntOperationFilterInput>;
  reportParentTypeOrder?: InputMaybe<IntOperationFilterInput>;
  reportTypeId?: InputMaybe<IntOperationFilterInput>;
  reportTypeName?: InputMaybe<StringOperationFilterInput>;
  reportTypeOrder?: InputMaybe<IntOperationFilterInput>;
  subTitle?: InputMaybe<StringOperationFilterInput>;
  thumbnailFileLocation?: InputMaybe<StringOperationFilterInput>;
  thumbnailFileLocationFull?: InputMaybe<StringOperationFilterInput>;
  title?: InputMaybe<StringOperationFilterInput>;
  uploadedDate?: InputMaybe<DateTimeOperationFilterInput>;
  uploadedDateUTC?: InputMaybe<DateTimeOperationFilterInput>;
  year?: InputMaybe<IntOperationFilterInput>;
};

export type ReportDtoSortInput = {
  fileLocation?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  languageCode?: InputMaybe<SortEnumType>;
  languageName?: InputMaybe<SortEnumType>;
  reportParentTypeDisplayName?: InputMaybe<SortEnumType>;
  reportParentTypeId?: InputMaybe<SortEnumType>;
  reportParentTypeOrder?: InputMaybe<SortEnumType>;
  reportTypeId?: InputMaybe<SortEnumType>;
  reportTypeName?: InputMaybe<SortEnumType>;
  reportTypeOrder?: InputMaybe<SortEnumType>;
  subTitle?: InputMaybe<SortEnumType>;
  thumbnailFileLocation?: InputMaybe<SortEnumType>;
  thumbnailFileLocationFull?: InputMaybe<SortEnumType>;
  title?: InputMaybe<SortEnumType>;
  uploadedDate?: InputMaybe<SortEnumType>;
  uploadedDateUTC?: InputMaybe<SortEnumType>;
  year?: InputMaybe<SortEnumType>;
};

/** A connection to a list of items. */
export type ReportsConnection = {
  __typename?: 'ReportsConnection';
  /** A list of edges. */
  edges?: Maybe<Array<ReportsEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Report>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

/** An edge in a connection. */
export type ReportsEdge = {
  __typename?: 'ReportsEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: Report;
};

export type Segment = {
  __typename?: 'Segment';
  name?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Array<SegmentValue>>;
};

export type SegmentDetail = {
  __typename?: 'SegmentDetail';
  detail?: Maybe<Array<Segment>>;
  name?: Maybe<Scalars['String']['output']>;
};

export type SegmentValue = {
  __typename?: 'SegmentValue';
  name?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

/** A segment of a collection. */
export type SegmentsCollectionSegment = {
  __typename?: 'SegmentsCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<SegmentsDto>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
};

export type SegmentsDto = {
  __typename?: 'SegmentsDto';
  acceptedDate?: Maybe<Scalars['DateTime']['output']>;
  calendarYear?: Maybe<Scalars['String']['output']>;
  cik?: Maybe<Scalars['String']['output']>;
  cumulativeType?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  date?: Maybe<Scalars['DateTime']['output']>;
  fillingDate?: Maybe<Scalars['DateTime']['output']>;
  finalLink?: Maybe<Scalars['String']['output']>;
  fye?: Maybe<Scalars['DateTime']['output']>;
  fys?: Maybe<Scalars['DateTime']['output']>;
  link?: Maybe<Scalars['String']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  periodEnd?: Maybe<Scalars['DateTime']['output']>;
  periodEndMonth?: Maybe<Scalars['String']['output']>;
  periodStart?: Maybe<Scalars['DateTime']['output']>;
  reportedCurrency?: Maybe<Scalars['String']['output']>;
  segments?: Maybe<Array<SegmentDetail>>;
  symbol?: Maybe<Scalars['String']['output']>;
  updateTimestamp?: Maybe<Scalars['DateTime']['output']>;
  year?: Maybe<Scalars['String']['output']>;
};

export type ShortOperationFilterInput = {
  eq?: InputMaybe<Scalars['Short']['input']>;
  gt?: InputMaybe<Scalars['Short']['input']>;
  gte?: InputMaybe<Scalars['Short']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Short']['input']>>>;
  lt?: InputMaybe<Scalars['Short']['input']>;
  lte?: InputMaybe<Scalars['Short']['input']>;
  neq?: InputMaybe<Scalars['Short']['input']>;
  ngt?: InputMaybe<Scalars['Short']['input']>;
  ngte?: InputMaybe<Scalars['Short']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['Short']['input']>>>;
  nlt?: InputMaybe<Scalars['Short']['input']>;
  nlte?: InputMaybe<Scalars['Short']['input']>;
};

export enum SortEnumType {
  Asc = 'ASC',
  Desc = 'DESC'
}

export type StockData_Deprecated = {
  __typename?: 'StockData_Deprecated';
  change?: Maybe<Scalars['Decimal']['output']>;
  changePercentage?: Maybe<Scalars['Decimal']['output']>;
  compares?: Maybe<Array<CompareData>>;
  date: Scalars['String']['output'];
  dateTime: Scalars['DateTime']['output'];
  dividendEvent: Scalars['Boolean']['output'];
  firstPrice?: Maybe<Scalars['Decimal']['output']>;
  highestPrice?: Maybe<Scalars['Decimal']['output']>;
  highestPriceDate?: Maybe<Scalars['DateTime']['output']>;
  highestVolumeDate?: Maybe<Scalars['DateTime']['output']>;
  instrumentId: Scalars['Int']['output'];
  lastPrice?: Maybe<Scalars['Decimal']['output']>;
  lowestPrice?: Maybe<Scalars['Decimal']['output']>;
  lowestPriceDate?: Maybe<Scalars['DateTime']['output']>;
  lowestVolumeDate?: Maybe<Scalars['DateTime']['output']>;
  mA10?: Maybe<Scalars['Decimal']['output']>;
  mA20?: Maybe<Scalars['Decimal']['output']>;
  mA50?: Maybe<Scalars['Decimal']['output']>;
  splitEvent: Scalars['String']['output'];
  totalReturn?: Maybe<Scalars['Decimal']['output']>;
  totalVolume?: Maybe<Scalars['Long']['output']>;
};

export type StringOperationFilterInput = {
  and?: InputMaybe<Array<StringOperationFilterInput>>;
  contains?: InputMaybe<Scalars['String']['input']>;
  endsWith?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  ncontains?: InputMaybe<Scalars['String']['input']>;
  nendsWith?: InputMaybe<Scalars['String']['input']>;
  neq?: InputMaybe<Scalars['String']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  nstartsWith?: InputMaybe<Scalars['String']['input']>;
  or?: InputMaybe<Array<StringOperationFilterInput>>;
  startsWith?: InputMaybe<Scalars['String']['input']>;
};

export type SubSector = {
  __typename?: 'SubSector';
  arabic?: Maybe<Scalars['String']['output']>;
  chinese?: Maybe<Scalars['String']['output']>;
  danish?: Maybe<Scalars['String']['output']>;
  description: Scalars['String']['output'];
  dutch?: Maybe<Scalars['String']['output']>;
  finnish?: Maybe<Scalars['String']['output']>;
  french?: Maybe<Scalars['String']['output']>;
  german?: Maybe<Scalars['String']['output']>;
  icelandic?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  italian?: Maybe<Scalars['String']['output']>;
  japanese?: Maybe<Scalars['String']['output']>;
  korean?: Maybe<Scalars['String']['output']>;
  marCat?: Maybe<Scalars['String']['output']>;
  marketId?: Maybe<Scalars['Byte']['output']>;
  norwegian?: Maybe<Scalars['String']['output']>;
  polish?: Maybe<Scalars['String']['output']>;
  russian?: Maybe<Scalars['String']['output']>;
  spanish?: Maybe<Scalars['String']['output']>;
  subSectorId2: Scalars['Int']['output'];
  swedish?: Maybe<Scalars['String']['output']>;
  taiwanese?: Maybe<Scalars['String']['output']>;
  vietnamese?: Maybe<Scalars['String']['output']>;
};

export type TimeFilterInput = {
  period?: InputMaybe<TimeStringFieldFilterInput>;
  year?: InputMaybe<TimeIntFieldFilterInput>;
};

export type TimeIntFieldFilterInput = {
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};

export type TimeSortInputSortInput = {
  date?: InputMaybe<SortEnumType>;
};

export type TimeStringFieldFilterInput = {
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type Timezone = HasTranslation & {
  __typename?: 'Timezone';
  diff: Scalars['Short']['output'];
  diffFromServerTime?: Maybe<Scalars['Short']['output']>;
  hour: Scalars['Float']['output'];
  /** Get IANA time zone name with ISO country name. */
  ianaByCountry?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  nameIANA?: Maybe<Scalars['String']['output']>;
  translation?: Maybe<Translation>;
  translationId?: Maybe<Scalars['Int']['output']>;
};


export type TimezoneIanaByCountryArgs = {
  countryName?: InputMaybe<Scalars['String']['input']>;
};


export type TimezoneTranslationArgs = {
  cultureName?: InputMaybe<Scalars['String']['input']>;
};

export type Translation = {
  __typename?: 'Translation';
  cultureName: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  value: Scalars['String']['output'];
};

export type Webcast = {
  __typename?: 'Webcast';
  companyCode: Scalars['String']['output'];
  createdBy: Scalars['String']['output'];
  createdDate: Scalars['DateTime']['output'];
  fileType: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  modifiedBy?: Maybe<Scalars['String']['output']>;
  modifiedDate?: Maybe<Scalars['DateTime']['output']>;
  publishDate: Scalars['DateTime']['output'];
  thumbnailType?: Maybe<Scalars['String']['output']>;
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  transcriptType?: Maybe<Scalars['String']['output']>;
  transcriptUrl?: Maybe<Scalars['String']['output']>;
  videos: Array<WebcastVideoDto>;
  vimeo?: Maybe<WebcastVimeoDto>;
  webcastHost: WebcastHostDto;
  webcastHostId: Scalars['Int']['output'];
  webcastSource: WebcastSourceDto;
  webcastSourceId: Scalars['Int']['output'];
  webcastType: WebcastTypeDto;
  webcastTypeId: Scalars['Int']['output'];
};

export type WebcastDetail_Deprecated = {
  __typename?: 'WebcastDetail_Deprecated';
  date?: Maybe<Scalars['DateTime']['output']>;
  defaultHost?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  transcriptUrl?: Maybe<Scalars['String']['output']>;
  urls?: Maybe<Array<WebcastVideo_Deprecated>>;
  videoType?: Maybe<Scalars['String']['output']>;
  vimeo?: Maybe<WebcastVimeo_Deprecated>;
};

export type WebcastDtoFilterInput = {
  and?: InputMaybe<Array<WebcastDtoFilterInput>>;
  companyCode?: InputMaybe<StringOperationFilterInput>;
  createdBy?: InputMaybe<StringOperationFilterInput>;
  createdDate?: InputMaybe<DateTimeOperationFilterInput>;
  fileType?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<LongOperationFilterInput>;
  modifiedBy?: InputMaybe<StringOperationFilterInput>;
  modifiedDate?: InputMaybe<DateTimeOperationFilterInput>;
  or?: InputMaybe<Array<WebcastDtoFilterInput>>;
  publishDate?: InputMaybe<DateTimeOperationFilterInput>;
  thumbnailType?: InputMaybe<StringOperationFilterInput>;
  thumbnailUrl?: InputMaybe<StringOperationFilterInput>;
  title?: InputMaybe<StringOperationFilterInput>;
  transcriptType?: InputMaybe<StringOperationFilterInput>;
  transcriptUrl?: InputMaybe<StringOperationFilterInput>;
  videos?: InputMaybe<ListFilterInputTypeOfWebcastVideoDtoFilterInput>;
  vimeo?: InputMaybe<WebcastVimeoDtoFilterInput>;
  webcastHost?: InputMaybe<WebcastHostDtoFilterInput>;
  webcastHostId?: InputMaybe<IntOperationFilterInput>;
  webcastSource?: InputMaybe<WebcastSourceDtoFilterInput>;
  webcastSourceId?: InputMaybe<IntOperationFilterInput>;
  webcastType?: InputMaybe<WebcastTypeDtoFilterInput>;
  webcastTypeId?: InputMaybe<IntOperationFilterInput>;
};

export type WebcastDtoSortInput = {
  publishDate?: InputMaybe<SortEnumType>;
};

export type WebcastHostDto = {
  __typename?: 'WebcastHostDto';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type WebcastHostDtoFilterInput = {
  and?: InputMaybe<Array<WebcastHostDtoFilterInput>>;
  id?: InputMaybe<IntOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<WebcastHostDtoFilterInput>>;
};

export type WebcastSourceDto = {
  __typename?: 'WebcastSourceDto';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type WebcastSourceDtoFilterInput = {
  and?: InputMaybe<Array<WebcastSourceDtoFilterInput>>;
  id?: InputMaybe<IntOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<WebcastSourceDtoFilterInput>>;
};

export type WebcastTypeDto = {
  __typename?: 'WebcastTypeDto';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type WebcastTypeDtoFilterInput = {
  and?: InputMaybe<Array<WebcastTypeDtoFilterInput>>;
  id?: InputMaybe<IntOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<WebcastTypeDtoFilterInput>>;
};

export type WebcastVideoDto = {
  __typename?: 'WebcastVideoDto';
  hostType?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  videoId?: Maybe<Scalars['String']['output']>;
};

export type WebcastVideoDtoFilterInput = {
  and?: InputMaybe<Array<WebcastVideoDtoFilterInput>>;
  hostType?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<WebcastVideoDtoFilterInput>>;
  url?: InputMaybe<StringOperationFilterInput>;
  videoId?: InputMaybe<StringOperationFilterInput>;
};

export type WebcastVideo_Deprecated = {
  __typename?: 'WebcastVideo_Deprecated';
  hostType?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  videoId?: Maybe<Scalars['String']['output']>;
};

export type WebcastVimeoDto = {
  __typename?: 'WebcastVimeoDto';
  hostType?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  videoId?: Maybe<Scalars['String']['output']>;
  vimeoId?: Maybe<Scalars['String']['output']>;
};

export type WebcastVimeoDtoFilterInput = {
  and?: InputMaybe<Array<WebcastVimeoDtoFilterInput>>;
  hostType?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<WebcastVimeoDtoFilterInput>>;
  url?: InputMaybe<StringOperationFilterInput>;
  videoId?: InputMaybe<StringOperationFilterInput>;
  vimeoId?: InputMaybe<StringOperationFilterInput>;
};

export type WebcastVimeo_Deprecated = {
  __typename?: 'WebcastVimeo_Deprecated';
  hostType?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  videoId?: Maybe<Scalars['String']['output']>;
  vimeoId?: Maybe<Scalars['String']['output']>;
};

/** A connection to a list of items. */
export type WebcastsConnection = {
  __typename?: 'WebcastsConnection';
  /** A list of edges. */
  edges?: Maybe<Array<WebcastsEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Webcast>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int']['output'];
};

/** An edge in a connection. */
export type WebcastsEdge = {
  __typename?: 'WebcastsEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node: Webcast;
};

export type YearlyPerformancePrice = {
  __typename?: 'YearlyPerformancePrice';
  changePercentage?: Maybe<Scalars['Decimal']['output']>;
  close?: Maybe<Scalars['Decimal']['output']>;
  closeLastYear?: Maybe<Scalars['Decimal']['output']>;
  year: Scalars['Int']['output'];
};

export type ChartHistoryQueryVariables = Exact<{
  id: Scalars['Int']['input'];
  fromDate?: InputMaybe<Scalars['DateTime']['input']>;
  toDate?: InputMaybe<Scalars['DateTime']['input']>;
  timeIntervalGrouping?: InputMaybe<Scalars['Int']['input']>;
}>;


export type ChartHistoryQuery = { __typename?: 'Query', instrumentById?: { __typename?: 'Instrument', market?: { __typename?: 'Market', openTimeLocal?: string | null, closeTimeLocal?: string | null, timezone?: { __typename?: 'Timezone', nameIANA?: string | null } | null } | null, intraday?: { __typename?: 'InstrumentIntradayConnection', nodes?: Array<{ __typename?: 'InstrumentHistory', close: number, dateTime: string, high?: number | null, low?: number | null, open?: number | null, volume?: number | null, instrumentId: number }> | null } | null } | null };

export type ChartHistoricalDataQueryVariables = Exact<{
  id: Scalars['Int']['input'];
  fromDate?: InputMaybe<Scalars['DateTime']['input']>;
  toDate?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type ChartHistoricalDataQuery = { __typename?: 'Query', instrumentById?: { __typename?: 'Instrument', historicals?: { __typename?: 'InstrumentHistoryConnection', nodes?: Array<{ __typename?: 'InstrumentHistory', close: number, dateTime: string, high?: number | null, low?: number | null, open?: number | null, volume?: number | null, instrumentId: number }> | null } | null } | null };

export type DividendEventsQueryVariables = Exact<{
  id: Scalars['Int']['input'];
  fromDate: Scalars['DateTime']['input'];
  toDate: Scalars['DateTime']['input'];
}>;


export type DividendEventsQuery = { __typename?: 'Query', instrumentById?: { __typename?: 'Instrument', dividends?: { __typename?: 'DividendsConnection', nodes?: Array<{ __typename?: 'Dividend', exDate?: string | null, grossDivAdj?: number | null, currency?: string | null }> | null } | null } | null };

export type DividendEventsPagingQueryVariables = Exact<{
  id: Scalars['Int']['input'];
  fromDate: Scalars['DateTime']['input'];
  toDate: Scalars['DateTime']['input'];
  cursor?: InputMaybe<Scalars['String']['input']>;
}>;


export type DividendEventsPagingQuery = { __typename?: 'Query', instrumentById?: { __typename?: 'Instrument', dividends?: { __typename?: 'DividendsConnection', edges?: Array<{ __typename?: 'DividendsEdge', node: { __typename?: 'Dividend', exDate?: string | null, grossDivAdj?: number | null, currency?: string | null } }> | null, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null } } | null } | null };

export type EarningEventsQueryVariables = Exact<{
  companyCode: Scalars['String']['input'];
  fromDate: Scalars['DateTime']['input'];
  toDate: Scalars['DateTime']['input'];
}>;


export type EarningEventsQuery = { __typename?: 'Query', company?: { __typename?: 'Company', fcEventsByTypes?: { __typename?: 'FCEventsByTypesConnection', nodes?: Array<{ __typename?: 'FCEvent', eventName: string, dateTime?: string | null }> | null } | null } | null };

export type EarningEventsPagingQueryVariables = Exact<{
  companyCode: Scalars['String']['input'];
  fromDate: Scalars['DateTime']['input'];
  toDate: Scalars['DateTime']['input'];
  cursor?: InputMaybe<Scalars['String']['input']>;
}>;


export type EarningEventsPagingQuery = { __typename?: 'Query', company?: { __typename?: 'Company', fcEventsByTypes?: { __typename?: 'FCEventsByTypesConnection', edges?: Array<{ __typename?: 'FCEventsByTypesEdge', node: { __typename?: 'FCEvent', eventName: string, dateTime?: string | null } }> | null, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null } } | null } | null };

export type MarketOfInstrumentQueryVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type MarketOfInstrumentQuery = { __typename?: 'Query', instrumentById?: { __typename?: 'Instrument', market?: { __typename?: 'Market', openTimeLocal?: string | null, closeTimeLocal?: string | null, status?: { __typename?: 'MarketStatus', isOpened: boolean, remainingTime: number } | null, timezone?: { __typename?: 'Timezone', nameIANA?: string | null } | null } | null } | null };

export type TickerQueryQueryVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type TickerQueryQuery = { __typename?: 'Query', instrumentById?: { __typename?: 'Instrument', symbol: string } | null };

export type TickerInitQueryVariables = Exact<{
  ids: Array<Scalars['Int']['input']> | Scalars['Int']['input'];
  toCurrency?: InputMaybe<Scalars['String']['input']>;
  adjClose?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type TickerInitQuery = { __typename?: 'Query', instrumentByIds?: Array<(
    { __typename?: 'Instrument' }
    & { ' $fragmentRefs'?: { 'TickerDataFragmentFragment': TickerDataFragmentFragment } }
  ) | null> | null };

export type TickerDataFragmentFragment = { __typename?: 'Instrument', shareName: string, id: number, symbol: string, low52W?: number | null, high52W?: number | null, currency?: { __typename?: 'Currency', code: string, name: string } | null, market?: { __typename?: 'Market', openTimeLocal?: string | null, closeTimeLocal?: string | null, status?: { __typename?: 'MarketStatus', isOpened: boolean, remainingTime: number } | null, timezone?: { __typename?: 'Timezone', nameIANA?: string | null } | null } | null, currentPrice?: { __typename?: 'InstrumentPrice', open?: number | null, prevClose?: number | null, volume?: number | null, officialClose?: number | null, officialCloseDate?: string | null, last?: number | null, change: number, changePercentage: number, low?: number | null, date?: string | null, bid?: number | null, ask?: number | null, high?: number | null } | null } & { ' $fragmentName'?: 'TickerDataFragmentFragment' };

export type TickerUpdateQueryVariables = Exact<{
  ids: Array<Scalars['Int']['input']> | Scalars['Int']['input'];
  adjClose?: InputMaybe<Scalars['Boolean']['input']>;
  additionalRealtimeIds: Array<Scalars['Int']['input']> | Scalars['Int']['input'];
  toCurrency?: InputMaybe<Scalars['String']['input']>;
}>;


export type TickerUpdateQuery = { __typename?: 'Query', instrumentByIds?: Array<(
    { __typename?: 'Instrument' }
    & { ' $fragmentRefs'?: { 'TickerDataFragment': TickerDataFragment } }
  ) | null> | null, additionalRealtime?: Array<{ __typename?: 'Instrument', id: number, currentPrice?: { __typename?: 'InstrumentPrice', officialClose?: number | null, officialCloseDate?: string | null } | null } | null> | null };

export type TickerDataFragment = { __typename?: 'Instrument', shareName: string, id: number, symbol: string, low52W?: number | null, high52W?: number | null, currency?: { __typename?: 'Currency', code: string, name: string } | null, market?: { __typename?: 'Market', openTimeLocal?: string | null, closeTimeLocal?: string | null, status?: { __typename?: 'MarketStatus', isOpened: boolean, remainingTime: number } | null, timezone?: { __typename?: 'Timezone', nameIANA?: string | null } | null } | null, currentPrice?: { __typename?: 'InstrumentPrice', open?: number | null, prevClose?: number | null, volume?: number | null, officialClose?: number | null, officialCloseDate?: string | null, last?: number | null, change: number, changePercentage: number, low?: number | null, date?: string | null, bid?: number | null, ask?: number | null, high?: number | null } | null } & { ' $fragmentName'?: 'TickerDataFragment' };

export const TickerDataFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TickerDataFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Instrument"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"shareName"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"symbol"}},{"kind":"Field","name":{"kind":"Name","value":"low52W"}},{"kind":"Field","name":{"kind":"Name","value":"high52W"}},{"kind":"Field","name":{"kind":"Name","value":"currency"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"market"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isOpened"}},{"kind":"Field","name":{"kind":"Name","value":"remainingTime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"openTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"closeTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"timezone"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nameIANA"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"currentPrice"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"open"}},{"kind":"Field","name":{"kind":"Name","value":"prevClose"}},{"kind":"Field","name":{"kind":"Name","value":"volume"}},{"kind":"Field","name":{"kind":"Name","value":"officialClose"}},{"kind":"Field","name":{"kind":"Name","value":"officialCloseDate"}},{"kind":"Field","name":{"kind":"Name","value":"last"}},{"kind":"Field","name":{"kind":"Name","value":"change"}},{"kind":"Field","name":{"kind":"Name","value":"changePercentage"}},{"kind":"Field","name":{"kind":"Name","value":"low"}},{"kind":"Field","name":{"kind":"Name","value":"date"}},{"kind":"Field","name":{"kind":"Name","value":"bid"}},{"kind":"Field","name":{"kind":"Name","value":"ask"}},{"kind":"Field","name":{"kind":"Name","value":"high"}}]}}]}}]} as unknown as DocumentNode<TickerDataFragmentFragment, unknown>;
export const TickerDataFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TickerData"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Instrument"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"shareName"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"symbol"}},{"kind":"Field","name":{"kind":"Name","value":"low52W"}},{"kind":"Field","name":{"kind":"Name","value":"high52W"}},{"kind":"Field","name":{"kind":"Name","value":"currency"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"market"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isOpened"}},{"kind":"Field","name":{"kind":"Name","value":"remainingTime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"openTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"closeTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"timezone"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nameIANA"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"currentPrice"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"open"}},{"kind":"Field","name":{"kind":"Name","value":"prevClose"}},{"kind":"Field","name":{"kind":"Name","value":"volume"}},{"kind":"Field","name":{"kind":"Name","value":"officialClose"}},{"kind":"Field","name":{"kind":"Name","value":"officialCloseDate"}},{"kind":"Field","name":{"kind":"Name","value":"last"}},{"kind":"Field","name":{"kind":"Name","value":"change"}},{"kind":"Field","name":{"kind":"Name","value":"changePercentage"}},{"kind":"Field","name":{"kind":"Name","value":"low"}},{"kind":"Field","name":{"kind":"Name","value":"date"}},{"kind":"Field","name":{"kind":"Name","value":"bid"}},{"kind":"Field","name":{"kind":"Name","value":"ask"}},{"kind":"Field","name":{"kind":"Name","value":"high"}}]}}]}}]} as unknown as DocumentNode<TickerDataFragment, unknown>;
export const ChartHistoryDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ChartHistory"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"timeIntervalGrouping"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"instrumentById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"market"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"timezone"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nameIANA"}}]}},{"kind":"Field","name":{"kind":"Name","value":"openTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"closeTimeLocal"}}]}},{"kind":"Field","name":{"kind":"Name","value":"intraday"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"dateTime"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"gte"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"lte"},"value":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}}}]}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"IntValue","value":"9999999"}},{"kind":"Argument","name":{"kind":"Name","value":"order"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"dateTime"},"value":{"kind":"EnumValue","value":"ASC"}}]}},{"kind":"Argument","name":{"kind":"Name","value":"timeIntervalGrouping"},"value":{"kind":"Variable","name":{"kind":"Name","value":"timeIntervalGrouping"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"close"}},{"kind":"Field","name":{"kind":"Name","value":"dateTime"}},{"kind":"Field","name":{"kind":"Name","value":"high"}},{"kind":"Field","name":{"kind":"Name","value":"low"}},{"kind":"Field","name":{"kind":"Name","value":"open"}},{"kind":"Field","name":{"kind":"Name","value":"volume"}},{"kind":"Field","name":{"kind":"Name","value":"instrumentId"}}]}}]}}]}}]}}]} as unknown as DocumentNode<ChartHistoryQuery, ChartHistoryQueryVariables>;
export const ChartHistoricalDataDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"ChartHistoricalData"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"instrumentById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"historicals"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"dateTime"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"gte"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"lte"},"value":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}}}]}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"IntValue","value":"9999999"}},{"kind":"Argument","name":{"kind":"Name","value":"order"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"dateTime"},"value":{"kind":"EnumValue","value":"ASC"}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"close"}},{"kind":"Field","name":{"kind":"Name","value":"dateTime"}},{"kind":"Field","name":{"kind":"Name","value":"high"}},{"kind":"Field","name":{"kind":"Name","value":"low"}},{"kind":"Field","name":{"kind":"Name","value":"open"}},{"kind":"Field","name":{"kind":"Name","value":"volume"}},{"kind":"Field","name":{"kind":"Name","value":"instrumentId"}}]}}]}}]}}]}}]} as unknown as DocumentNode<ChartHistoricalDataQuery, ChartHistoricalDataQueryVariables>;
export const DividendEventsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"DividendEvents"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"instrumentById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"dividends"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"exDate"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"gte"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"lt"},"value":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}}}]}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"IntValue","value":"99999999"}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"exDate"}},{"kind":"Field","name":{"kind":"Name","value":"grossDivAdj"}},{"kind":"Field","name":{"kind":"Name","value":"currency"}}]}}]}}]}}]}}]} as unknown as DocumentNode<DividendEventsQuery, DividendEventsQueryVariables>;
export const DividendEventsPagingDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"DividendEventsPaging"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"cursor"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"instrumentById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"dividends"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"exDate"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"gte"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"lt"},"value":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}}}]}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"IntValue","value":"200"}},{"kind":"Argument","name":{"kind":"Name","value":"after"},"value":{"kind":"Variable","name":{"kind":"Name","value":"cursor"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"edges"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"node"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"exDate"}},{"kind":"Field","name":{"kind":"Name","value":"grossDivAdj"}},{"kind":"Field","name":{"kind":"Name","value":"currency"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"endCursor"}}]}}]}}]}}]}}]} as unknown as DocumentNode<DividendEventsPagingQuery, DividendEventsPagingQueryVariables>;
export const EarningEventsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EarningEvents"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"companyCode"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"company"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"code"},"value":{"kind":"Variable","name":{"kind":"Name","value":"companyCode"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fcEventsByTypes"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"fcEventTypeNames"},"value":{"kind":"StringValue","value":"Results","block":false}},{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"dateTime"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"gte"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"lt"},"value":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}}}]}}]}},{"kind":"Argument","name":{"kind":"Name","value":"order"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"dateTime"},"value":{"kind":"EnumValue","value":"ASC"}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"IntValue","value":"99999999"}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nodes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eventName"}},{"kind":"Field","name":{"kind":"Name","value":"dateTime"}}]}}]}}]}}]}}]} as unknown as DocumentNode<EarningEventsQuery, EarningEventsQueryVariables>;
export const EarningEventsPagingDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EarningEventsPaging"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"companyCode"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"cursor"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"company"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"code"},"value":{"kind":"Variable","name":{"kind":"Name","value":"companyCode"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fcEventsByTypes"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"fcEventTypeNames"},"value":{"kind":"StringValue","value":"Results","block":false}},{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"dateTime"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"gte"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fromDate"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"lt"},"value":{"kind":"Variable","name":{"kind":"Name","value":"toDate"}}}]}}]}},{"kind":"Argument","name":{"kind":"Name","value":"order"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"dateTime"},"value":{"kind":"EnumValue","value":"DESC"}}]}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"IntValue","value":"200"}},{"kind":"Argument","name":{"kind":"Name","value":"after"},"value":{"kind":"Variable","name":{"kind":"Name","value":"cursor"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"edges"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"node"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eventName"}},{"kind":"Field","name":{"kind":"Name","value":"dateTime"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"endCursor"}}]}}]}}]}}]}}]} as unknown as DocumentNode<EarningEventsPagingQuery, EarningEventsPagingQueryVariables>;
export const MarketOfInstrumentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"MarketOfInstrument"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"instrumentById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"market"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isOpened"}},{"kind":"Field","name":{"kind":"Name","value":"remainingTime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"openTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"closeTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"timezone"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nameIANA"}}]}}]}}]}}]}}]} as unknown as DocumentNode<MarketOfInstrumentQuery, MarketOfInstrumentQueryVariables>;
export const TickerQueryDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TickerQuery"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"instrumentById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"symbol"}}]}}]}}]} as unknown as DocumentNode<TickerQueryQuery, TickerQueryQueryVariables>;
export const TickerInitDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TickerInit"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"ids"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"toCurrency"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"adjClose"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"instrumentByIds"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"ids"}}},{"kind":"Argument","name":{"kind":"Name","value":"exchangeCurrency"},"value":{"kind":"Variable","name":{"kind":"Name","value":"toCurrency"}}},{"kind":"Argument","name":{"kind":"Name","value":"adjClose"},"value":{"kind":"Variable","name":{"kind":"Name","value":"adjClose"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"TickerDataFragment"}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TickerDataFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Instrument"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"shareName"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"symbol"}},{"kind":"Field","name":{"kind":"Name","value":"low52W"}},{"kind":"Field","name":{"kind":"Name","value":"high52W"}},{"kind":"Field","name":{"kind":"Name","value":"currency"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"market"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isOpened"}},{"kind":"Field","name":{"kind":"Name","value":"remainingTime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"openTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"closeTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"timezone"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nameIANA"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"currentPrice"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"open"}},{"kind":"Field","name":{"kind":"Name","value":"prevClose"}},{"kind":"Field","name":{"kind":"Name","value":"volume"}},{"kind":"Field","name":{"kind":"Name","value":"officialClose"}},{"kind":"Field","name":{"kind":"Name","value":"officialCloseDate"}},{"kind":"Field","name":{"kind":"Name","value":"last"}},{"kind":"Field","name":{"kind":"Name","value":"change"}},{"kind":"Field","name":{"kind":"Name","value":"changePercentage"}},{"kind":"Field","name":{"kind":"Name","value":"low"}},{"kind":"Field","name":{"kind":"Name","value":"date"}},{"kind":"Field","name":{"kind":"Name","value":"bid"}},{"kind":"Field","name":{"kind":"Name","value":"ask"}},{"kind":"Field","name":{"kind":"Name","value":"high"}}]}}]}}]} as unknown as DocumentNode<TickerInitQuery, TickerInitQueryVariables>;
export const TickerUpdateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TickerUpdate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"ids"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"adjClose"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"additionalRealtimeIds"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"toCurrency"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"instrumentByIds"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"ids"}}},{"kind":"Argument","name":{"kind":"Name","value":"exchangeCurrency"},"value":{"kind":"Variable","name":{"kind":"Name","value":"toCurrency"}}},{"kind":"Argument","name":{"kind":"Name","value":"adjClose"},"value":{"kind":"Variable","name":{"kind":"Name","value":"adjClose"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"TickerData"}}]}},{"kind":"Field","alias":{"kind":"Name","value":"additionalRealtime"},"name":{"kind":"Name","value":"instrumentByIds"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"additionalRealtimeIds"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"currentPrice"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"officialClose"}},{"kind":"Field","name":{"kind":"Name","value":"officialCloseDate"}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"TickerData"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Instrument"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"shareName"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"symbol"}},{"kind":"Field","name":{"kind":"Name","value":"low52W"}},{"kind":"Field","name":{"kind":"Name","value":"high52W"}},{"kind":"Field","name":{"kind":"Name","value":"currency"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"market"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isOpened"}},{"kind":"Field","name":{"kind":"Name","value":"remainingTime"}}]}},{"kind":"Field","name":{"kind":"Name","value":"openTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"closeTimeLocal"}},{"kind":"Field","name":{"kind":"Name","value":"timezone"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"nameIANA"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"currentPrice"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"open"}},{"kind":"Field","name":{"kind":"Name","value":"prevClose"}},{"kind":"Field","name":{"kind":"Name","value":"volume"}},{"kind":"Field","name":{"kind":"Name","value":"officialClose"}},{"kind":"Field","name":{"kind":"Name","value":"officialCloseDate"}},{"kind":"Field","name":{"kind":"Name","value":"last"}},{"kind":"Field","name":{"kind":"Name","value":"change"}},{"kind":"Field","name":{"kind":"Name","value":"changePercentage"}},{"kind":"Field","name":{"kind":"Name","value":"low"}},{"kind":"Field","name":{"kind":"Name","value":"date"}},{"kind":"Field","name":{"kind":"Name","value":"bid"}},{"kind":"Field","name":{"kind":"Name","value":"ask"}},{"kind":"Field","name":{"kind":"Name","value":"high"}}]}}]}}]} as unknown as DocumentNode<TickerUpdateQuery, TickerUpdateQueryVariables>;