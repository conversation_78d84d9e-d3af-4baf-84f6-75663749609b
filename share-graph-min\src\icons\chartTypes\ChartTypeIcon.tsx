import { ISvgIconProps } from "../../types/common";

const ChartTypeIcon: React.FC<ISvgIconProps> = ({ width = 16, height = 16 }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={26}
      height={26}
      viewBox="0 0 26 26"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      style={{color: 'currentColor', width: width, height: height}}
    >
      <path d="M9 5v4" />
      <rect width={4} height={6} x={7} y={9} rx={1} />
      <path d="M9 15v2" />
      <path d="M17 3v2" />
      <rect width={4} height={8} x={15} y={5} rx={1} />
      <path d="M17 13v3" />
      <path d="M3 3v18h18" />
    </svg>
  );
};

export default ChartTypeIcon;
