@use "./variables" as *;

@font-face {
    font-family: $fontInter;
    font-weight: 300;
    font-display: swap;
    src: url("/src/assets/fonts/inter/Inter-Light.ttf");
  }
  
  @font-face {
    font-family: $fontInter;
    font-weight: 400;
    font-display: swap;
    src: url("/src/assets/fonts/inter/Inter-Regular.ttf");
  }
  
  @font-face {
    font-family: $fontInter;
    font-weight: 500;
    font-display: swap;
    src: url("/src/assets/fonts/inter/Inter-Medium.ttf");
  }
  
  @font-face {
    font-family: $fontInter;
    font-weight: 600;
    font-display: swap;
    src: url("/src/assets/fonts/inter/Inter-SemiBold.ttf");
  }
  
  @font-face {
    font-family: $fontInter;
    font-weight: 700;
    font-display: swap;
    src: url("/src/assets/fonts/inter/Inter-Bold.ttf");
  }

@font-face {
    font-family: $fontOpensans;
    font-weight: 300;
    font-display: swap;
    src: url("/src/assets/fonts/opensans/OpenSans-Light.ttf") format('truetype');
  }
  
  @font-face {
    font-family: $fontOpensans;
    font-weight: 400;
    font-display: swap;
    src: url("/src/assets/fonts/opensans/OpenSans-Regular.ttf") format('truetype');
  }
  
  @font-face {
    font-family: $fontOpensans;
    font-weight: 500;
    font-display: swap;
    src: url("/src/assets/fonts/opensans/OpenSans-Medium.ttf") format('truetype');
  }
  
  @font-face {
    font-family: $fontOpensans;
    font-weight: 600;
    font-display: swap;
    src: url("/src/assets/fonts/opensans/OpenSans-SemiBold.ttf") format('truetype');
  }
  
  @font-face {
    font-family: $fontOpensans;
    font-weight: 700;
    font-display: swap;
    src: url("/src/assets/fonts/opensans/OpenSans-Bold.ttf") format('truetype');
  }