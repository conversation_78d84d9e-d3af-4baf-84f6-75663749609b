import {I<PERSON>hart<PERSON>pi} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {merge} from "es-toolkit";

interface IChartIndicatorConstructor<IOptions extends ChartIndicatorOptions = ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[]> {
  new (
    chart: IChartApi,
    options?: Partial<IOptions>,
    paneIndex?: number
  ): ChartIndicator<IOptions, IIndicatorData >;
}

export class IndicatorFactory {
  private static registry = new Map<string, [IChartIndicatorConstructor, object | undefined]>();

  static registerIndicator<IOptions extends ChartIndicatorOptions, IIndicatorData extends readonly number[] = number[]>(name: string, indicatorClass: IChartIndicatorConstructor<IOptions, IIndicatorData>, defaultOptions?: Partial<IOptions>) {
    this.registry.set(name, [indicatorClass as unknown as IChartIndicatorConstructor, defaultOptions]);
  }

  static indicatorRegistered(name: string) {
    return this.registry.has(name);
  }

  static createIndicator(name: string, chartApi: IChartApi, options?: Partial<ChartIndicatorOptions>, paneIndex?: number) {
    const result = this.registry.get(name);
    if (!result) {
      throw new Error(`Indicator "${name}" not registered. Available: ${Array.from(this.registry.keys()).join(', ')}`);
    }

    const [IndicatorClass, defaultSettings] = result;
    return new IndicatorClass(chartApi, (options || defaultSettings) ? merge(structuredClone(defaultSettings) ?? {}, options ?? {}) : undefined, paneIndex);
  }
}
