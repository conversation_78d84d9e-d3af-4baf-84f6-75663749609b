import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from '../../../lightweight-chart/advance-chart';
import { MMIndicator } from '../../../lightweight-chart/indicators';

const MACDLegend: FC<{
  indicator: MMIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="MM"
      indicator={indicator}
      renderer={(d) => d && d.value ? (
        <>
          <span
            style={{
              color:
                (d.value[2] ?? 0) > 0
                  ? indicator.options.upColor
                  : indicator.options.downColor,
            }}
          >
            {advanceChart.numberFormatter.decimal(d.value[2])}
          </span>{' '}
          <span style={{ color: indicator.options.MMLineColor }}>
            {advanceChart.numberFormatter.decimal(d.value[0])}
          </span>{' '}
          <span
            style={{
              color: indicator.options.signalLineColor,
            }}
          >
            {advanceChart.numberFormatter.decimal(d.value[1])}
          </span>
        </>
      ) : null}
    />
  );
};

export default MACDLegend;
