import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from '../../../lightweight-chart/advance-chart';
import { EMAIndicator } from '../../../lightweight-chart/indicators';

const EMALegend: FC<{
  indicator: EMAIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="EMA"
      indicator={indicator}
      renderer={(d) =>
        d ? (
          <>
            {' '}
            <span style={{ color: indicator.options.color }}>
              {advanceChart.numberFormatter.decimal(d.value.at(0))}
            </span>{' '}
          </>
        ) : null
      }
    />
  );
};

export default EMALegend;
