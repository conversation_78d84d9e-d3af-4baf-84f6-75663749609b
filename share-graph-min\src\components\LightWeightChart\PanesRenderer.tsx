import { FC, ReactNode, useEffect, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { AdvanceChart,  } from '../../lightweight-chart/advance-chart';
import { useChartContext } from './context';
import {IGroupIndicatorByPane} from '../../lightweight-chart/advance-chart/i-advance-chart';

const PanesRenderer: FC<{
  render: (pane: IGroupIndicatorByPane, advanceChart: AdvanceChart) => ReactNode;
}> = ({ render }) => {
  const { getChart } = useChartContext();
  const [panes, panesSet] = useState<
    { paneGroup: IGroupIndicatorByPane; container: HTMLElement }[]
  >([]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;

    const handle = () => {
      const result = chart
        .groupIndicatorByPane()
        .map((paneGroup) => ({
          paneGroup,
          container: paneGroup.pane.getHTMLElement().children[1] as HTMLElement,
        }));
      panesSet(result);
    };

    const retry = () => {
      try {
        handle();
      } catch {
        requestAnimationFrame(handle)
      }
    };
    retry();
    chart.updated().subscribe(retry);
    return () => chart.updated().unsubscribe(retry);
  }, [getChart]);

  const chart = getChart()

  return (
    <>
      {useMemo(
        () =>
          chart ? panes.map(({ paneGroup, container }) =>
            createPortal(render(paneGroup, chart), container)
          ) : null,
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [panes]
      )}
    </>
  );
};

export default PanesRenderer;
