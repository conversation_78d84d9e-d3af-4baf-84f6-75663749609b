import React, { useEffect, useState } from 'react';
import {
  DropdownMenuItem,
  DropdownMenuItemId,
  OnChangeDropdown,
} from '../../types/common';
import { IChartSetting } from '../../types/store';
import clsx from 'clsx';
import useClickOutside from '../../hooks/useClickOutside';
import Switcher from '../Switcher';

interface DropdownProps {
  menuData?: DropdownMenuItem[];
  value?: IChartSetting;
  placeholder: React.ReactNode;
  parentIdFromProp?: DropdownMenuItemId;
  position?: 'left' | 'right';
  className?: string;
  onChange?: OnChangeDropdown;
  description?: string;
}

const Dropdown: React.FC<DropdownProps> = ({
  menuData = [],
  value,
  onChange,
  placeholder,
  parentIdFromProp,
  position = 'right',
  className,
  description,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const ref = useClickOutside<HTMLDivElement>(() => {
    setShowDropdown(false);
  });

  useEffect(() => {
    const handleFocus = (e) => {
      if (ref.current && !ref.current.contains(e.target)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('focusin', handleFocus);

    return () => {
      document.removeEventListener('focusin', handleFocus);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePressKey = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Escape':
        setShowDropdown(false);
        return;

      case 'ArrowUp':
        console.log('ArrowUp');

        return;

      case 'ArrowDown':
        console.log('ArrowDown');

        return;

      default:
        return;
    }
  };

  const handleItemClick = (
    item: DropdownMenuItem,
    parentId: DropdownMenuItemId
  ) => {
    if (!item.items) {
      onChange?.(item.id, parentId);
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLUListElement>) => {
    e.stopPropagation();
  };

  const handleWheel = (e: React.WheelEvent<HTMLUListElement>) => {
    e.stopPropagation();
  };

  const renderMenuItem = ({
    item,
    parentId,
  }: {
    item: DropdownMenuItem;
    parentId: DropdownMenuItemId;
  }) => {
    if (item.items) {
      return (
        <React.Fragment key={item.id}>
          {!item.hideGroupLabel && (
            <li className="dropdown-item dropdown-item__heading">
              <div className="dropdown-item__separator"></div>
              <p className="dropdown-item__label">{item.label}</p>
            </li>
          )}
          {item.items.map((subItem) =>
            renderMenuItem({
              item: subItem,
              parentId,
            })
          )}
        </React.Fragment>
      );
    }
    const newParentId = item.parentId || parentIdFromProp || parentId;
    const isActive = value?.[newParentId]?.includes(item.id);
    const isShowActiveLabel = item.activeLabel && isActive;

    let icon = item.icon;
    if (isActive && item.activeIcon) icon = item.activeIcon;

    if (item.switcher) {
      return (
        <li className="dropdown-item dropdown-item__switcher" key={item.id}>
        <span>{isShowActiveLabel ? item.activeLabel : item.label}</span>

          <Switcher checked={isActive} onChange={() => handleItemClick(item, newParentId)} />
        </li>
      );
    }

    return (
      <li
        className={clsx('dropdown-item', {
          selected: isActive,
          disabled: item.disabled,
        })}
        onClick={() =>
          item.disabled ? null : handleItemClick(item, newParentId)
        }
        key={item.id}
      >
        {icon && <span className="dropdown-item__icon">{icon}</span>}
        <span>{isShowActiveLabel ? item.activeLabel : item.label}</span>
      </li>
    );
  };

  return (
    <div
      className={clsx('dropdown', Boolean(className) && className)}
      ref={ref}
      onKeyDown={handlePressKey}
    >
      <button
        className="dropdown-trigger"
        onClick={() => setShowDropdown((prev) => !prev)}
      >
        {placeholder}
      </button>
      <ul
        className={clsx('dropdown-menu', position, {
          show: showDropdown,
        })}
        onScroll={handleScroll}
        onWheel={handleWheel}
      >
        {description && (
          <li className="dropdown-item dropdown-item__description">
            <p className="dropdown-item__description-label">{description}</p>
          </li>
        )}
        {menuData.map((item) =>
          renderMenuItem({
            item,
            parentId: item.id,
          })
        )}
      </ul>
    </div>
  );
};

export default Dropdown;
