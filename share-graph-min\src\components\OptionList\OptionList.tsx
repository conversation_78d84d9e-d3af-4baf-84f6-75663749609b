import clsx from 'clsx';
import React from 'react';
import { IOptionList } from '../../types/OptionList';

interface OptionListProps {
  title: string;
  options: IOptionList[];
  value: IOptionList['id'][];
  onChange: (id: IOptionList['id'], option: IOptionList) => void;
}

const OptionList: React.FC<OptionListProps> = ({
  title,
  options = [],
  value = [],
  onChange,
}) => {
  const handleSelectValue = (tab: IOptionList) => {
    if (tab.disabled) return;
    onChange(tab.id, tab);
  };

  return (
    <div className="option-list">
      <h3 className="option-list__title">{title}</h3>
      <ul className="option-list__menu">
        {options.map((tab) => (
          <li className="option-list__menu__item" key={tab.id}>
            <button
              className={clsx('option-list__tab', {
                active: value.includes(tab.id),
                disabled: Boolean(tab.disabled),
              })}
              onClick={() => handleSelectValue(tab)}
            >
              {tab.label}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default OptionList;
