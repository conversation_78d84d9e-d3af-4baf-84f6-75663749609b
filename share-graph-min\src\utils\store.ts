import { MAX_SELECTED_OPTIONS } from "../constants/common";
import { ITickerData } from "../services/ticker-services";
import { ITickerDataStore } from "../stores/slices/tickerSlice";


interface IGetOptionValueParams {
    oldData: string[];
    newValue: string;
    maxSelectedOption: number;
}

// Updates the list of selected options based on the user's selection action.
export const getCheckboxOptionValue = ({
    oldData,
    newValue,
    maxSelectedOption,
}: IGetOptionValueParams): string[] => {
    const isMaxSelected = oldData.length >= maxSelectedOption;
    const isAlreadySelected = oldData.includes(newValue);
    const isOnlySelected = maxSelectedOption === 1 ;

    if (isAlreadySelected) return oldData.filter((ind) => ind !== newValue);
    if (!isMaxSelected) return [...oldData, newValue]
    if (isOnlySelected) return [newValue];
    return oldData
}

export const getRadioOptionValue = ({
    oldData,
    newValue,
}: Omit<IGetOptionValueParams, 'maxSelectedOption'>): string[] => {
    const isAlreadySelected = oldData.includes(newValue);
    return isAlreadySelected ? oldData : [newValue];
}

export const getMaxSelectedOption = (maxSelected?: number): number => maxSelected || MAX_SELECTED_OPTIONS.DEFAULT;

export const getTickerDataInStore = (tickerData: ITickerData[]): ITickerDataStore => {
    const newTickers = tickerData.reduce((acc, item) => {
        return {
            ...acc,
            [item.id]: item
        }
    }, {})
    return newTickers;
};


export const convertInstrumentIdsToArr = (instrumentIds: string): number[] => {
    return instrumentIds.split(',').map(Number);
};

export const calcChangeAndChangePercentage = ({ last, prevClose }: {
    last?: number;
    prevClose?: number;
}) => {
    if (last === undefined || prevClose === undefined) return {
        change: undefined
        , changePercentage: undefined
    }
    const change = last - prevClose;
    const changePercentage = (change / prevClose) * 100;
    return { change, changePercentage }
}
