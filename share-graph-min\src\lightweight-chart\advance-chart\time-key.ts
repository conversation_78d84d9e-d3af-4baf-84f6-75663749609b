import {Time, defaultHorzScaleBehavior} from "lightweight-charts"
import {Interval, Period} from "./i-advance-chart"

const horzScaleBehavior = new (defaultHorzScaleBehavior())

const MINUTE_IN_SECONDS = 60
const HOUR_IN_SECONDS = MINUTE_IN_SECONDS * 60
const DAY_IN_SECONDS = HOUR_IN_SECONDS * 24
const WEEK_IN_SECONDS = DAY_IN_SECONDS * 7
const START_OF_WEEK_OFFSET = DAY_IN_SECONDS * 3 // because timestamp 0 is 1/1/1970 which is a Thursday

export function timeKey(time: Time, interval: Interval) {
  const unixTime = horzScaleBehavior.key(time)
  const period = interval.period
  const times = interval.times
  
  switch(period) {
    case Period.minute: {
      return unixTime - (unixTime % (MINUTE_IN_SECONDS * times))
    }
    case Period.hour: {
      return unixTime - (unixTime % (HOUR_IN_SECONDS * times))
    }
    case Period.day: {
        return unixTime - (unixTime % (DAY_IN_SECONDS * times))
    }
    case Period.week: {
      const newUnixTime = unixTime + START_OF_WEEK_OFFSET
      return unixTime - (newUnixTime % (WEEK_IN_SECONDS * times))
    }
    case Period.month: {
      const monthDate = new Date(unixTime * 1000)
      return Math.floor(Date.UTC(monthDate.getUTCFullYear(), monthDate.getUTCMonth(), 1, 0, 0, 0) / 1000)
    }
    default: 
      throw new Error('Invalid period')
  }
}   