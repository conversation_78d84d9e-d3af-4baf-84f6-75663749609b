import { useState, useEffect } from 'react';

/**
 * Custom hook to detect if the viewport matches a specific media query.
 * Handles browser resize events automatically.
 *
 * @param query - The media query to check against (e.g. '(max-width: 768px)')
 * @returns boolean indicating if the media query matches
 */
function useMediaQuery(query: string): boolean {
  // Initialize with a function to check the match on initial render
  const [matches, setMatches] = useState(() => window.matchMedia(query).matches);

  useEffect(() => {

    const mediaQuery = window.matchMedia(query);
    const handleChange = (e: MediaQueryListEvent) => {
      setMatches(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [query]);

  return matches;
}

export default useMediaQuery; 