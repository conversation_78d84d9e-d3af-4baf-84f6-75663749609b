import { i18n } from '@euroland/libs';
import {
  CHART_EVENT_KEYS,
} from '../../constants/chartConstant';
import OptionList from './OptionList';
import { IOptionList } from '../../types/OptionList';
import { useAppStore } from '../../stores/useAppStore';
import { useMemo } from 'react';

const EventsOptionList = () => {
  const menuData: IOptionList[] = [
    {
      id: CHART_EVENT_KEYS.DIVIDEND.key,
      label: i18n.translate(CHART_EVENT_KEYS.DIVIDEND.label),
    },
    {
      id: CHART_EVENT_KEYS.EARNING.key,
      label: i18n.translate(CHART_EVENT_KEYS.EARNING.label),
    },
  ];

  const enableEvents = useAppStore((state) => state.enableEvents);
  const setChartEvents = useAppStore((state) => state.setChartEvents);

  const eventValue = useMemo(() => {
      const value: string[] = [];
      Object.keys(enableEvents).forEach((key) => {
        if (enableEvents[key]) {
          value.push(key);
        }
      });
      return value;
    }, [enableEvents]);

  const handleChangeValue = (optionId: string) => {
    setChartEvents(optionId);
  };

  return (
    <OptionList
      title={i18n.translate('events')}
      options={menuData}
      onChange={handleChangeValue}
      value={eventValue}
    />
  );
};

export default EventsOptionList;
