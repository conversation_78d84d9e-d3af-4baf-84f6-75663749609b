const DotIcon = ({style}: {style?: React.CSSProperties}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-circle h-2 w-2 fill-current"
      style={{ width: 8, height: 8, fill: 'currentColor', ...style }}
    >
      <circle cx={12} cy={12} r={10} />
    </svg>
  );
};

export default DotIcon;
