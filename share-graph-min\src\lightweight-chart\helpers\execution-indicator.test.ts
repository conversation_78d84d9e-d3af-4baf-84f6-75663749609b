import { describe, it, expect } from 'vitest';
import { ExecutionContext, Context, ISymbolData } from './execution-indicator';

describe('ExecutionContext', () => {
  // Helper to create sample data (reuse pattern)
  const createSampleData = (count = 3): ISymbolData[] => {
    return Array.from({ length: count }, (_, i) => ({
      time: i + 1,
      open: 100 + i * 5,
      high: 110 + i * 5,
      low: 90 + i * 5,
      close: 105 + i * 5,
      volume: 1000 + i * 100
    }));
  };

  // Helper formula: Simple 2-period moving average
  const createSMAFormula = (periods = 2) => {
    return (c: Context): [number] | undefined => {
      const close = c.symbol.close;
      const prev = c.new_var(close, periods);
      
      if (!prev.calculable()) return undefined;
      
      const sum = Array.from({ length: periods }, (_, i) => prev.get(i)).reduce((a, b) => a + b, 0);
      return [sum / periods];
    };
  };

  // Helper formula: Multi-value indicator (like Bollinger Bands)
  const createBollingerFormula = () => {
    return (c: Context): [number, number, number] | undefined => {
      const close = c.symbol.close;
      const prev = c.new_var(close, 2);
      
      if (!prev.calculable()) return undefined;
      
      const middle = (prev.get(0) + prev.get(1)) / 2;
      return [middle - 10, middle, middle + 10]; // Lower, Middle, Upper
    };
  };

  describe('basic functionality', () => {
    it('should initialize with empty data', () => {
      const ec = new ExecutionContext(createSMAFormula());
      
      expect(ec.data).toEqual([]);
    });

    it('should calculate indicator values for dataset', () => {
      const ec = new ExecutionContext(createSMAFormula());
      const data = createSampleData(3);
      
      ec.recalc(data);
      
      // Test core behavior: calculates moving average
      expect(ec.data).toHaveLength(3);
      expect(ec.data[0].value).toBeUndefined(); // Not enough data
      expect(ec.data[1].value).toEqual([107.5]); // (105 + 110) / 2
      expect(ec.data[2].value).toEqual([112.5]); // (110 + 115) / 2
    });

    it('should handle multi-value indicators', () => {
      const ec = new ExecutionContext(createBollingerFormula());
      const data = createSampleData(3);
      
      ec.recalc(data);
      
      // Test core behavior: returns multiple values per point
      expect(ec.data[1].value).toEqual([97.5, 107.5, 117.5]);
      expect(ec.data[2].value).toEqual([102.5, 112.5, 122.5]);
    });
  });

  describe('data updates', () => {
    it('should update existing point with same time', () => {
      const ec = new ExecutionContext(createSMAFormula());
      const data = createSampleData(3);
      ec.recalc(data);
      
      const newPoint = { time: 3, open: 110, high: 125, low: 105, close: 120, volume: 1400 };
      ec.update(newPoint);
      
      // Test core behavior: updates last point in place
      expect(ec.data).toHaveLength(3);
      expect(ec.data[2].value).toEqual([115]); // (110 + 120) / 2
    });

    it('should add new point with different time', () => {
      const ec = new ExecutionContext(createSMAFormula());
      const data = createSampleData(3);
      ec.recalc(data);
      
      const newPoint = { time: 4, open: 115, high: 125, low: 110, close: 120, volume: 1500 };
      ec.update(newPoint);
      
      // Test core behavior: adds new point
      expect(ec.data).toHaveLength(4);
      expect(ec.data[3].value).toEqual([117.5]); // (115 + 120) / 2
    });

    it('should preserve earlier data points when updating', () => {
      const ec = new ExecutionContext(createSMAFormula());
      const data = createSampleData(3);
      ec.recalc(data);
      
      const initialSecondValue = ec.data[1].value;
      
      ec.update({ time: 3, open: 110, high: 125, low: 105, close: 120, volume: 1400 });
      
      // Test core behavior: only affects target point
      expect(ec.data[1].value).toEqual(initialSecondValue);
    });
  });

  describe('edge cases', () => {
    it('should handle empty dataset', () => {
      const ec = new ExecutionContext(createSMAFormula());
      
      ec.recalc([]);
      
      expect(ec.data).toEqual([]);
    });

    it('should handle insufficient data for calculation', () => {
      const ec = new ExecutionContext(createSMAFormula());
      const singlePoint = createSampleData(1);
      
      ec.recalc(singlePoint);
      
      // Test core behavior: graceful handling of insufficient data
      expect(ec.data).toHaveLength(1);
      expect(ec.data[0].value).toBeUndefined();
    });

    it('should handle formula that returns undefined', () => {
      const alwaysUndefinedFormula = () => undefined;
      const ec = new ExecutionContext(alwaysUndefinedFormula);
      
      ec.recalc(createSampleData(3));
      
      // Test core behavior: handles formula failures gracefully
      expect(ec.data).toHaveLength(3);
      expect(ec.data.every(item => item.value === undefined)).toBe(true);
    });
  });

  describe('context variable operations', () => {
    it('should provide access to historical data through context variables', () => {
      const historyTestFormula = (c: Context): [number, number, number] | undefined => {
        const close = c.symbol.close;
        const prev = c.new_var(close, 3);
        
        if (!prev.calculable()) return undefined;
        
        return [prev.get(0), prev.get(1), prev.get(2)]; // Current, previous, 2 periods ago
      };
      
      const ec = new ExecutionContext(historyTestFormula);
      ec.recalc(createSampleData(4));
      
      // Test core behavior: context provides historical access
      expect(ec.data[2].value).toEqual([115, 110, 105]);
      expect(ec.data[3].value).toEqual([120, 115, 110]);
    });

    it('should track multiple variables independently', () => {
      const multiVariableFormula = (c: Context): [number, number] | undefined => {
        const high = c.symbol.high;
        const low = c.symbol.low;
        const prevHigh = c.new_var(high, 2);
        const prevLow = c.new_var(low, 2);
        
        if (!prevHigh.calculable() || !prevLow.calculable()) return undefined;
        
        const avgHigh = (prevHigh.get(0) + prevHigh.get(1)) / 2;
        const avgLow = (prevLow.get(0) + prevLow.get(1)) / 2;
        
        return [avgHigh, avgLow];
      };
      
      const ec = new ExecutionContext(multiVariableFormula);
      ec.recalc(createSampleData(3));
      
      // Test core behavior: handles multiple context variables
      // createSampleData(3) generates: 
      // [0]: high=110, low=90
      // [1]: high=115, low=95  
      // [2]: high=120, low=100
      // So at index 1: avgHigh=(115+110)/2=112.5, avgLow=(95+90)/2=92.5
      expect(ec.data[1].value).toEqual([112.5, 92.5]); // Avg of highs and lows
      expect(ec.data[2].value).toEqual([117.5, 97.5]); // (120+115)/2, (100+95)/2
    });
  });
}); 