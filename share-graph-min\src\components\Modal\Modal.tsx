import React from "react";
import CloseIcon from "../../icons/CloseIcon";
import Button from "../Button";
import { i18n } from "@euroland/libs";

type ModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onOk?: () => void;
  title?: string;
  children: React.ReactNode;
  okText?: string;
  cancelText?: string;
};

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  onOk,
  title,
  children,
  cancelText,
  okText,
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal">
      <div className="modal__overlay" onClick={onClose} />
      <div className="modal__content">
        <div className="modal__header">
          {title && <h2 className="modal__title">{title}</h2>}
          <button className="modal__close" onClick={onClose}>
            <CloseIcon />
          </button>
        </div>
        <div className="modal__body">{children}</div>
        <div className="modal__footer">
          <Button onClick={onClose}>{cancelText || i18n.translate("cancel")}</Button>
          <Button type="secondary" onClick={() => onOk?.()}>
            {okText || i18n.translate("ok")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Modal;
