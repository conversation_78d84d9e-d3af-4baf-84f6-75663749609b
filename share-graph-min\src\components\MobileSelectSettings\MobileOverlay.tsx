import React, { useState } from 'react';
import OverlayIcon from '../../icons/OverlayIcon';
import Button from '../Button';
import { i18n } from '@euroland/libs';
import { OverlayDrawer } from '../Drawer';

const MobileOverlay = () => {
  const [isOverlayDrawerOpen, setIsOverlayDrawerOpen] = useState(false);

  const toggleOverlayDrawer = () => {
    setIsOverlayDrawerOpen(!isOverlayDrawerOpen);
  };

  return (
    <>
      <Button onClick={toggleOverlayDrawer} className="mobile-button">
        <OverlayIcon /> <span>{i18n.translate('overlays')}</span>
      </Button>
      <OverlayDrawer
        isOpen={isOverlayDrawerOpen}
        onClose={toggleOverlayDrawer}
      />
    </>
  );
};

export default React.memo(MobileOverlay); 