import { useAppStore } from '../../stores/useAppStore';
import clsx from 'clsx';

const InstrumentRadio = () => {
  const instrumentIds = useAppStore((state) => state.appSettings.instrumentIds);
  const tickers = useAppStore((state) => state.tickers);
  const changeSelectedInstrumentId = useAppStore(
    (state) => state.changeSelectedInstrumentId
  );
  const selectedInstrumentId = useAppStore((state) => state.selectedInstrumentId);

  // const instrumentIdInArr = useMemo(
  //   () => convertInstrumentIdsToArr(instrumentIds),
  //   [instrumentIds]
  // );

  if (instrumentIds.length <= 1) return null;

  return (
    <div className="multiple-shares">
      <ul className="multiple-shares__list">
        {instrumentIds.map((insId) => {
          const tickerInfo = tickers[insId];
          if (!tickerInfo) return null;

          return (
            <li key={insId} className="multiple-shares__item">
              <button
                className={clsx('multiple-shares__button', {
                  active: insId === selectedInstrumentId,
                })}
                onClick={() => changeSelectedInstrumentId(insId)}
              >
                {tickerInfo.symbol}
              </button>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default InstrumentRadio;
