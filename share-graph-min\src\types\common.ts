import { TTimeIntervals, TChartRangePeriod } from "./store";

export type DropdownMenuItemId = string;

export type OnChangeDropdown = (val: DropdownMenuItemId, parentId: DropdownMenuItemId) => void;

export interface DropdownMenuItem {
    id: DropdownMenuItemId;
    label?: string;
    activeLabel?: string;
    icon?: React.ReactNode;
    activeIcon?: React.ReactNode;
    items?: DropdownMenuItem[];
    hideGroupLabel?: boolean;
    disabled?: boolean;
    switcher?: boolean; // if true, the item will be a switcher
    parentId?: DropdownMenuItemId;
}

export interface ITimelineOption {
    period: TChartRangePeriod;
    label: string;
    intervals: TTimeIntervals[];
    defaultSelectedInterval: TTimeIntervals;
    fromDateSubtract: {
        number: number;
        unit: 'day' | 'month' | 'year';
    } // to calculate fromDate from toDate (toDate is now)
  }

  export interface ISvgIconProps {
    width?: number;
    height?: number;
    color?: string;
    className?: string;
  }

  export type TCSSVariables = {
    [key: string]: string; // key is the css variable name, value is the css variable value
  };

