import { OhlcData } from 'lightweight-charts';
import { mergeOhlcData } from './mergeData';
import { describe, expect, it } from 'vitest';

describe('mergeOhlcData', () => {
  // Helper to create OHLC data (reuse pattern)
  const createOhlcData = (time: string | number, basePrice = 100): OhlcData => ({
    time: time as OhlcData['time'],
    open: basePrice,
    high: basePrice + 10,
    low: basePrice - 10,
    close: basePrice + 5
  });

  // Helper to create data arrays
  const createDataArray = (times: (string | number)[], startPrice = 100): OhlcData[] => {
    return times.map((time, i) => createOhlcData(time, startPrice + i * 10));
  };

  describe('basic merging', () => {
    it('should return new data when old data is empty', () => {
      const newData = createDataArray(['2023-01-01', '2023-01-02']);
      
      const result = mergeOhlcData([], newData);
      
      expect(result).toBe(newData);
    });

    it('should return old data when new data is empty', () => {
      const oldData = createDataArray(['2023-01-01', '2023-01-02']);
      
      const result = mergeOhlcData(oldData, []);
      
      expect(result).toBe(oldData);
    });

    it('should return empty array when both arrays are empty', () => {
      const result = mergeOhlcData([], []);
      
      expect(result).toEqual([]);
    });
  });

  describe('non-overlapping data', () => {
    it('should concatenate when old data is entirely before new data', () => {
      const oldData = createDataArray(['2023-01-01', '2023-01-02'], 100);
      const newData = createDataArray(['2023-01-03', '2023-01-04'], 200);
      
      const result = mergeOhlcData(oldData, newData);
      
      // Test core behavior: maintains chronological order
      expect(result).toEqual([...oldData, ...newData]);
      expect(result).toHaveLength(4);
    });

    it('should merge when new data is entirely before old data', () => {
      const oldData = createDataArray([5, 6], 500);
      const newData = createDataArray([3, 4], 300);
      
      const result = mergeOhlcData(oldData, newData);
      
      // Test core behavior: new data comes first, then old data
      expect(result).toEqual([...newData, ...oldData]);
    });
  });

  describe('overlapping data', () => {
    it('should replace overlapping points with newer data', () => {
      const oldData: OhlcData[] = [
        { time: '2023-01-01', open: 10, high: 15, low: 8, close: 12 },
        { time: '2023-01-02', open: 12, high: 16, low: 10, close: 14 },
        { time: '2023-01-03', open: 14, high: 18, low: 12, close: 16 },
      ];

      const newData: OhlcData[] = [
        { time: '2023-01-02', open: 13, high: 17, low: 11, close: 15 }, // Overlaps
        { time: '2023-01-03', open: 15, high: 19, low: 13, close: 17 }, // Overlaps
        { time: '2023-01-04', open: 17, high: 21, low: 15, close: 19 }, // New
      ];

      const result = mergeOhlcData(oldData, newData);

      // Test core behavior: newer data overwrites old data at same time
      expect(result).toEqual([
        { time: '2023-01-01', open: 10, high: 15, low: 8, close: 12 },  // Preserved
        { time: '2023-01-02', open: 13, high: 17, low: 11, close: 15 }, // Replaced
        { time: '2023-01-03', open: 15, high: 19, low: 13, close: 17 }, // Replaced
        { time: '2023-01-04', open: 17, high: 21, low: 15, close: 19 }, // Added
      ]);
    });

    it('should handle partial overlap at boundary', () => {
      const oldData = createDataArray([1, 2, 3], 100);
      const newData = createDataArray([3, 4], 300); // Overlaps at time=3
      
      const result = mergeOhlcData(oldData, newData);
      
      // Test core behavior: boundary point is replaced, tail is preserved
      expect(result).toHaveLength(4);
      expect(result[2].open).toBe(300); // New data overwrites time=3
      expect(result[3].open).toBe(310); // New data adds time=4
    });

    it('should preserve data beyond new data range', () => {
      const oldData = createDataArray([1, 2, 3, 4, 5], 100);
      const newData = createDataArray([3, 4], 300);
      
      const result = mergeOhlcData(oldData, newData);
      
      // Test core behavior: old data beyond new range is preserved
      expect(result).toHaveLength(5);
      // createDataArray([1,2,3,4,5], 100) generates:
      // time=5 gets basePrice=100+(4*10)=140 (0-indexed, so index 4)
      expect(result[4].open).toBe(140); // Original time=5 data preserved
    });
  });
});
