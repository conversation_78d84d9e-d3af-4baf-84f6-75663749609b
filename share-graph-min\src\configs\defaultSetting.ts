import { CHART_EVENT_KEYS, CHART_TYPE_KEYS, DEFAULT_STORE_CHART_SETTINGS } from "../constants/chartConstant";
import { IDefaultSettingConfigs } from "../types/defaultConfig";


export const FONTS = {
    SYSTEM: {
        key: 'system',
        value: '-apple-system, BlinkMacSystemFont, Trebuchet MS, Roboto, Ubuntu, sans-serif'
    },
    ARIAL: {
        key: 'arial',
        value: 'Arial, sans-serif'
    },
    INTER: {
        key: 'inter',
        value: 'Inter, sans-serif'
    },
    OPEN_SANS: {
        key: 'open-sans',
        value: 'OpenSans, sans-serif'
    },
}

export const HAS_BOOLEAN_VALUE_KEYS = ['customRange'];

export const DEFAULT_CHART_HEIGHT = 400;

const DEFAULT_SETTING: IDefaultSettingConfigs = {
    defaultSelectedInstrumentId: 0,
    instrumentIds: [], // all instrument ids 
    companyCode: '',
    realtimeIds: [],
    settingPosition: 'top',
    dateRangesAndInterval: [
        {
            period: '1D',
            intervals: ["1m", "5m", "10m", "15m", "30m", "1h"],
        },
        
          {
            period: "5D",
            intervals: ["1m", "5m", "10m", "15m", "30m", "1h"],
          },
          {
            period: "1M",
            intervals: ["daily", "weekly"],
          },
          {
            period: "3M",
            intervals: ["daily", "weekly"],
          },
          {
            period: "6M",
            intervals: ["daily", "weekly"],
          },
          {
            period: "1Y",
            intervals: ["daily", "weekly", "monthly"],
          },
          {
            period: "5Y",
            intervals: ["daily", "weekly", "monthly"],
          },
          {
            period: "10Y",
            intervals: ["daily", "weekly", "monthly"],
          },
        
    ],
    defaultRange: {
        period: '1M',
        interval: 'daily',
    },
    general: {
        locale: 'en',
        fontFamily: FONTS.SYSTEM.key,
        fontSize: '14', // px
        fontColor: '#131722',
        upColor: '#26a69a',
        downColor: '#ef5350',
        primaryColor: '#2962ff'
    },
    chartConfiguration: {
        gridColor: '#f2f2f2',
        axesFontsize: '14', // px
        axesColor: '#131722',
        height: DEFAULT_CHART_HEIGHT, // px
        chartType: CHART_TYPE_KEYS.MOUNTAIN.key,
        ...DEFAULT_STORE_CHART_SETTINGS
    },
    tickerSettings: {
        dataFields: 'open,high,low,close,volume',
        template: 'ticker',
        refreshTickerTime: 60, // second
    },
    valueTracking: 'legend',
    events: `${CHART_EVENT_KEYS.EARNING.key}`, // earning, dividend
    customRange: true,
    unControllerUI: '', // volume,y-axis-preferences
    footerText: 'Designed by Notified. Powered by Euroland IR.',
}

export const url = 'http://localhost:5173/?realtimeIds=32864&events=earning&tooltip=tooltip&volume=show-hide,underlay&show-last-close-line=show-last-close-line&chartType=line&width=100&height=100&instrumentId=32864&defaultRange=1M%2Cdaily&dateRangesAndInterval=1D;5D%7C1m,5m,10m&locale=en&fontFamily=Arial&fontSize=20&fontColor=%23131722&upColor=green&downColor=red&primaryColor=%232962ff&gridColor=%232962ff&axesFontsize=20&axesColor=%23131722&dataFields=open,high,low,close,volume&template=ticker&valueTracking=legend,customRange=true';

export default DEFAULT_SETTING;