import { useEffect, useMemo, useState } from 'react';
import { useChartContext } from '../context';
import { useAppStore } from '../../../stores/useAppStore';
import { CHART_EVENT_KEYS } from '../../../constants/chartConstant';
import { getFromAndToDateByDataFeed } from '../../../utils/marker';
import EventPoint from './EventPoint';
import { IEarningEvent, IEventPoints } from '../../../types/events';
import EarningHandler from './utils/earning-marker';
import dayjs from '../../../lightweight-chart/helpers/dayjs-setup'
import {findNearestPosition} from './utils/findNearestPosition';

const EarningMarker = () => {
  const enableEvents = useAppStore((state) => state.enableEvents);
  const enableEarning = enableEvents[CHART_EVENT_KEYS.EARNING.key];
  const { getChart, getDataFeed } = useChartContext();
  const [points, setPoints] = useState<IEventPoints<IEarningEvent>[]>([]);

  const earningInstance = useMemo(() => new EarningHandler(undefined, undefined), [])

  useEffect(() => {
    const dataFeed = getDataFeed();
    if(!dataFeed) return;

    const handle = () => {
      
      const chartData = dataFeed.data

      if (chartData.length === 0) return;
      const { fromDate, toDate } = getFromAndToDateByDataFeed(chartData);
      if (enableEarning) earningInstance.load(fromDate, toDate);
    }
    dataFeed.dataChanged().subscribe(handle)
    handle()

    return () => dataFeed.dataChanged().unsubscribe(handle)
  }, [enableEarning, getDataFeed, earningInstance]);



  useEffect(() => {
    const chart = getChart();

    if (!chart) return;
    const drawEarning = () => {
      
      const newPoints: IEventPoints<IEarningEvent>[] = [];
      const earningData:IEarningEvent[]  = earningInstance.getData()
      const timeScale = chart.chartApi.timeScale()
      const mainSeries = chart.mainSeries;
      if(!mainSeries) return;
     
      earningData.forEach((point) => {
        const xPosi = findNearestPosition(point.dateTime, timeScale, mainSeries);

        if(!xPosi) {
          console.warn('Can not find xPosition for earning point', point)
          return;
        }
        newPoints.push({
          xPosition: xPosi,
          metaData: point,
        });
      });

      setPoints(newPoints.filter((point) => point.xPosition));
    };


    drawEarning();
    chart.chartApi.timeScale().subscribeVisibleLogicalRangeChange(drawEarning);

    earningInstance.dataLoaded.subscribe(drawEarning);

    return () => {
      earningInstance.dataLoaded.unsubscribe(drawEarning);
      chart.chartApi
        .timeScale()
        .unsubscribeVisibleLogicalRangeChange(drawEarning);
    };
  }, [getChart, enableEarning, earningInstance]);

  if (!enableEarning) return null;

  return (
    <>
      {points.map((point, index) => (
        <EventPoint xPosition={point.xPosition} key={index} type="earning">
          <h3 className='earing-marker__title'>{point.metaData.eventName}</h3>
          <time>{dayjs.tz(point.metaData.dateTime, 'UTC').format('MM/DD/YYYY')}</time>
        </EventPoint>
      ))}
    </>
  );
};

export default EarningMarker;
