

import { StateCreator } from "zustand"
import { TStoreState } from "../useAppStore"
import { getInitTicker, getUpdateTicker, ITickerData } from "../../services/ticker-services"
import { calcChangeAndChangePercentage, getTickerDataInStore } from "../../utils/store"
import { IRealtimeData } from "../../realtime/realtimeSetup"
import dayjs from "../../lightweight-chart/helpers/dayjs-setup"
import { produce } from "immer"
import { isTwoDateAfterOrEqual } from "../../utils/common"

export interface ITickerDataStore {
    [key: string]: ITickerData
}

export interface ITickerStore {
    tickers: ITickerDataStore
    fetchInitTickers: () => void
    fetchUpdateTickers: () => void
    updateRealtimeTickerData: (data: IRealtimeData) => void
}

export const createTickerSlice: StateCreator<
    TStoreState,
    [],
    [],
    ITickerStore
> = (set, get) => ({
    tickers: {},
    fetchInitTickers: async () => {
        const { appSettings } = get();
        const tickerData = await getInitTicker(appSettings.instrumentIds);

        if (tickerData) {
            const newTickers = getTickerDataInStore(tickerData)
            set({ tickers: newTickers })
        }
    },
    fetchUpdateTickers: async () => {
        const { appSettings, tickers } = get();
        const tickerData = await getUpdateTicker(appSettings.instrumentIds)

        if (tickerData) {
            return set(produce((state) => { 
                tickerData.forEach(ticker => {
                    const needUpdateTicker = tickers[ticker.id];
                    if (!needUpdateTicker) return;
    
                    const isAfterOrEqual = isTwoDateAfterOrEqual({
                        date1: dayjs(ticker.currentPrice.date),
                        date2: dayjs(needUpdateTicker.currentPrice.date)
                    })
    
                    if (!isAfterOrEqual) return;
                    state.tickers[ticker.id] = ticker;
    
                })
                
        
            }))
        }
    },

    updateRealtimeTickerData: (realtimeData) =>
    {
        const { tickers } = get();
        const { id, last } = realtimeData;
        const needUpdateTicker = tickers[id];
        if (!needUpdateTicker) return;
        return set(produce((state) => { 
            
            const { change, changePercentage } = calcChangeAndChangePercentage({ last, prevClose: needUpdateTicker.currentPrice.prevClose });
            const updateArr = ['open', 'last', 'low', 'high', 'volume', 'date'];
            updateArr.forEach((key) => {
                const value = realtimeData[key];
                if (value !== undefined) state.tickers[id].currentPrice[key] = value;
            });
            if (change) state.tickers[id].currentPrice.change = change;
            if (changePercentage) state.tickers[id].currentPrice.changePercentage = changePercentage;
    
        }))
    }
})



