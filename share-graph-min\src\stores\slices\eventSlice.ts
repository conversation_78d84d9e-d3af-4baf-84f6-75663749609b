

import { StateCreator } from "zustand"
import { TStoreState } from "../useAppStore"

export interface IEventStore {
    events: string[],
    enableEvents: { [key: string]: boolean },
    setChartEvents: (selectedValue: string) => void;
}

export const createEventsSlice: StateCreator<
    TStoreState,
    [],
    [],
    IEventStore
> = (set, get) => ({
    events: [],
    enableEvents: {},
    dividendEventsData: [],
    earningEventsData: [],
 
    setChartEvents: (values) => {
        const { enableEvents } = get();
        const valArr = values.split(',');
        const newEnableEvents = valArr.reduce((acc, val) => {
            const oldValue = enableEvents[val];
            return {
                ...acc,
                [val]: !oldValue,
            }
        }, enableEvents);
        set(() => ({
            enableEvents: newEnableEvents,
        }))
    },
})
