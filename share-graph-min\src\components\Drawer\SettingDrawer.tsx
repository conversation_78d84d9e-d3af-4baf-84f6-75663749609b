import { useState, useEffect } from 'react';
import { i18n } from '@euroland/libs';
import { useAppStore } from '../../stores/useAppStore';
import {
  CHART_EVENT_KEYS,
  CHART_SETTING_KEYS,
} from '../../constants/chartConstant';
import { TChartSetting } from '../../types/store';
import Drawer from './Drawer';
import OptionsList from './OptionsList';

interface SettingDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const SettingDrawer: React.FC<SettingDrawerProps> = ({ isOpen, onClose }) => {
  const chartSettings = useAppStore((state) => state.chartSettings);
  const setChartSettings = useAppStore((state) => state.setChartSettings);
  const setChartEvents = useAppStore((state) => state.setChartEvents);
  const enableEvents = useAppStore((state) => state.enableEvents);

  // Group options by section
  const chartPreferencesOptions = [
    {
      id: CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.key,
      label: i18n.translate(
        CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.label
      ),
      parentId: CHART_SETTING_KEYS.CHART_PREFERENCES.key,

    },
    {
      id: CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key,
      label: i18n.translate(CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.label),
      parentId: CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key,

    },
  ];

  const eventsOptions = [
    {
      id: CHART_EVENT_KEYS.DIVIDEND.key,
      label: i18n.translate(CHART_EVENT_KEYS.DIVIDEND.label),

    },
    {
      id: CHART_EVENT_KEYS.EARNING.key,
      label: i18n.translate(CHART_EVENT_KEYS.EARNING.label),

    },
  ];

  const [selectedPreferences, setSelectedPreferences] = useState<string[]>([]);
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);

  // Sync with store data
  useEffect(() => {
    let preferences: string[] = [];
    for (const key in chartSettings) {
      const item = chartSettings[key];
      preferences = preferences.concat(item);
    }

    setSelectedPreferences(preferences);
  }, [chartSettings]);

  useEffect(() => {
    const events: string[] = [];
    Object.keys(enableEvents).forEach((key) => {
      if (enableEvents[key]) {
        events.push(key);
      }
    });
    setSelectedEvents(events);
  }, [enableEvents]);

  // Handle toggling chart preferences
  const togglePreference = (preferenceId: string, item) => {
    setChartSettings(item.parentId as TChartSetting, preferenceId);
  };

  const toggleEvent = (eventId: string) => {
    setChartEvents(eventId);
  };

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      title={i18n.translate('settings')}
    >
      <div className="setting-drawer">
        <div className="setting-drawer__section">
          <OptionsList
            options={chartPreferencesOptions}
            selectedOptions={selectedPreferences}
            onToggleOption={togglePreference}
          />
        </div>

        <div className="setting-drawer__section">
          <h4 className="setting-drawer__section-title">
            {i18n.translate('events')}
          </h4>
          <OptionsList
            options={eventsOptions}
            selectedOptions={selectedEvents}
            onToggleOption={toggleEvent}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default SettingDrawer;
