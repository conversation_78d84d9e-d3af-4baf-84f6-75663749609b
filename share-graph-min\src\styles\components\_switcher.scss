.switcher {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  border-radius: 10px;
  background-color: #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
  box-sizing: border-box;
  
  &__toggle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #fff;
    transition: transform 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  &--checked {
    background-color: #000;
    
    .switcher__toggle {
      transform: translateX(20px);
    }
  }
  
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  }
  
  &:focus:not(:focus-visible) {
    box-shadow: none;
  }
} 