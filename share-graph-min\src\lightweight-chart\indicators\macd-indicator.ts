import {HistogramData, HistogramSeries, IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions, downColor, upColor} from "./abstract-indicator";
import {EMA, SMA} from "technicalindicators";
import {Context} from "../helpers/execution-indicator";

export interface MACDIndicatorOptions extends ChartIndicatorOptions {
  fastPeriod: number,
  slowPeriod: number,
  signalPeriod: number,
  SimpleMAOscillator: boolean,
  SimpleMASignal: boolean,
  upColor: string,
  downColor: string,
  macdLineColor: string,
  signalLineColor: string
};

export const defaultOptions: MACDIndicatorOptions = {
  fastPeriod: 12,
  slowPeriod: 26,
  signalPeriod: 9,
  SimpleMAOscillator: false,
  SimpleMASignal: false,
  upColor: upColor,
  downColor: downColor,
  macdLineColor: '#2b97f1',
  signalLineColor: '#fd6c1c',
  overlay: false
}

export type MacdMACDData = Nominal<number, 'Macd'>
export type SignalMACDData = Nominal<number, 'Signal'>
export type HistogramMACDData = Nominal<number, 'Histogram'>
export type MACDData = [MacdMACDData, SignalMACDData, HistogramMACDData]

export default class MACDIndicator extends ChartIndicator<MACDIndicatorOptions, MACDData> {
  histogramSeries: ISeriesApi<'Histogram'>
  macdSeries: ISeriesApi<SeriesType>
  signalSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<MACDIndicatorOptions>, paneIndex?: number) {
    super(chart, options);

    this.histogramSeries = chart.addSeries(HistogramSeries, {
      priceLineVisible: false,
      priceScaleId: 'macd'
    }, paneIndex)

    this.macdSeries = chart.addSeries(LineSeries, {
      color: this.options.macdLineColor,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'macd'
    }, paneIndex)

    this.signalSeries = chart.addSeries(LineSeries, {
      color: this.options.signalLineColor,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'macd'
    }, paneIndex)
  }

  applyIndicatorData(): void {
    const histogramData: HistogramData[] = []
    const macdData: SingleValueData[] = []
    const signalData: SingleValueData[] = []

    for(const bar of this._executionContext.data) {
      const value = bar.value;
      const time = bar.time as Time;
      
      if(!value) continue;
      const [macd, signal, histogram] = value;
      if(!isNaN(macd)) macdData.push({time, value: macd})
      if(!isNaN(signal)) signalData.push({time, value: signal})
      if(!isNaN(histogram)) histogramData.push({time, value: histogram, color: (histogram ?? 0) >= 0 ? this.options.upColor : this.options.downColor})
    }

    this.histogramSeries.setData(histogramData)
    this.macdSeries.setData(macdData)
    this.signalSeries.setData(signalData)
  }

  formula(c: Context): MACDData | undefined {
      const fastPeriodSeries = c.new_var(c.symbol.close, this.options.fastPeriod)
      const slowPeriodSeries = c.new_var(c.symbol.close, this.options.slowPeriod)
      const signalPeriodSeries = c.new_var(NaN, this.options.signalPeriod)

      if(!fastPeriodSeries.calculable() || !slowPeriodSeries.calculable()) return;

      const oscillatorMAtype = this.options.SimpleMAOscillator ? SMA : EMA;
      const signalMAtype = this.options.SimpleMASignal ? SMA : EMA;
      const [fastMA] = new oscillatorMAtype({ period: this.options.fastPeriod, values: fastPeriodSeries.getAll()}).result;
      const [slowMA] = new oscillatorMAtype({ period: this.options.slowPeriod, values: slowPeriodSeries.getAll()}).result;
      const macd = fastMA - slowMA
      signalPeriodSeries.set(macd);
      if(!signalPeriodSeries.calculable()) return;
      const [signalMA] = new signalMAtype({ period: this.options.signalPeriod, values: signalPeriodSeries.getAll()}).result;

      const histogram = macd - signalMA;

      return [macd as MacdMACDData, signalMA as SignalMACDData, histogram as HistogramMACDData]
  }

  _applyOptions(options: Partial<MACDIndicatorOptions>): void {
    if(
      options.SimpleMAOscillator || 
      options.SimpleMASignal ||
      options.fastPeriod ||
      options.signalPeriod ||
      options.slowPeriod
    ) {
      this.calcIndicatorData()
    } 

    if(options.macdLineColor) this.macdSeries.applyOptions({color: options.macdLineColor})
    if(options.signalLineColor) this.signalSeries.applyOptions({color: options.signalLineColor})
    
    if(options.downColor || options.upColor) this.applyIndicatorData()
  }

  getDefaultOptions() {
    return defaultOptions
  }

  remove(): void {
    super.remove()
    this.chart.removeSeries(this.histogramSeries)
    this.chart.removeSeries(this.macdSeries)
    this.chart.removeSeries(this.signalSeries)
  }

  setPaneIndex(paneIndex: number): void {
    this.histogramSeries.moveToPane(paneIndex);
    this.macdSeries.moveToPane(paneIndex);
    this.signalSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.histogramSeries.getPane().paneIndex()
  }
}
