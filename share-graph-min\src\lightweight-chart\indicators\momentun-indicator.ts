import {HistogramData, HistogramSeries, IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions, downColor, upColor} from "./abstract-indicator";
import {EMA, SMA} from "technicalindicators";
import {Context} from "../helpers/execution-indicator";

export interface MMIndicatorOptions extends ChartIndicatorOptions {
  fastPeriod: number,
  slowPeriod: number,
  signalPeriod: number,
  SimpleMAOscillator: boolean,
  SimpleMASignal: boolean,
  upColor: string,
  downColor: string,
  MMLineColor: string,
  signalLineColor: string
};

export const defaultOptions: MMIndicatorOptions = {
  fastPeriod: 12,
  slowPeriod: 26,
  signalPeriod: 9,
  SimpleMAOscillator: false,
  SimpleMASignal: false,
  upColor: upColor,
  downColor: downColor,
  MMLineColor: '#2b97f1',
  signalLineColor: '#fd6c1c',
  overlay: false
}

export type MMMMData = Nominal<number, 'MM'>
export type SignalMMData = Nominal<number, 'Signal'>
export type HistogramMMData = Nominal<number, 'Histogram'>
export type MMData = [MMMMData, SignalMMData, HistogramMMData]

export default class MMIndicator extends ChartIndicator<MMIndicatorOptions, MMData> {
  histogramSeries: ISeriesApi<'Histogram'>
  MMSeries: ISeriesApi<SeriesType>
  signalSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<MMIndicatorOptions>, paneIndex?: number) {
    super(chart, options);

    this.histogramSeries = chart.addSeries(HistogramSeries, {
      priceLineVisible: false,
      priceScaleId: 'momentum'
    }, paneIndex)

    this.MMSeries = chart.addSeries(LineSeries, {
      color: this.options.MMLineColor,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'momentum'
    }, paneIndex)

    this.signalSeries = chart.addSeries(LineSeries, {
      color: this.options.signalLineColor,
      lineWidth: 1,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'momentum'
    }, paneIndex)
  }

  applyIndicatorData(): void {
    const histogramData: HistogramData[] = []
    const MMData: SingleValueData[] = []
    const signalData: SingleValueData[] = []

    for(const bar of this._executionContext.data) {
      const value = bar.value;
      const time = bar.time as Time;
      
      if(!value) continue;
      const [momentum, signal, histogram] = value;
      if(!isNaN(momentum)) MMData.push({time, value: momentum})
      if(!isNaN(signal)) signalData.push({time, value: signal})
      if(!isNaN(histogram)) histogramData.push({time, value: histogram, color: (histogram ?? 0) >= 0 ? this.options.upColor : this.options.downColor})
    }

    this.histogramSeries.setData(histogramData)
    this.MMSeries.setData(MMData)
    this.signalSeries.setData(signalData)
  }

  formula(c: Context): MMData | undefined {
      const fastPeriodSeries = c.new_var(c.symbol.close, this.options.fastPeriod)
      const slowPeriodSeries = c.new_var(c.symbol.close, this.options.slowPeriod)
      const signalPeriodSeries = c.new_var(NaN, this.options.signalPeriod)

      if(!fastPeriodSeries.calculable() || !slowPeriodSeries.calculable()) return;

      const oscillatorMAtype = this.options.SimpleMAOscillator ? SMA : EMA;
      const signalMAtype = this.options.SimpleMASignal ? SMA : EMA;
      const [fastMA] = new oscillatorMAtype({ period: this.options.fastPeriod, values: fastPeriodSeries.getAll()}).result;
      const [slowMA] = new oscillatorMAtype({ period: this.options.slowPeriod, values: slowPeriodSeries.getAll()}).result;
      const momentum = fastMA - slowMA
      signalPeriodSeries.set(momentum);
      if(!signalPeriodSeries.calculable()) return;
      const [signalMA] = new signalMAtype({ period: this.options.signalPeriod, values: signalPeriodSeries.getAll()}).result;

      const histogram = momentum - signalMA;

      return [momentum as MMMMData, signalMA as SignalMMData, histogram as HistogramMMData]
  }

  _applyOptions(options: Partial<MMIndicatorOptions>): void {
    if(
      options.SimpleMAOscillator || 
      options.SimpleMASignal ||
      options.fastPeriod ||
      options.signalPeriod ||
      options.slowPeriod
    ) {
      this.calcIndicatorData()
    } 

    if(options.MMLineColor) this.MMSeries.applyOptions({color: options.MMLineColor})
    if(options.signalLineColor) this.signalSeries.applyOptions({color: options.signalLineColor})
    
    if(options.downColor || options.upColor) this.applyIndicatorData()
  }

  getDefaultOptions() {
    return defaultOptions
  }

  remove(): void {
    super.remove()
    this.chart.removeSeries(this.histogramSeries)
    this.chart.removeSeries(this.MMSeries)
    this.chart.removeSeries(this.signalSeries)
  }

  setPaneIndex(paneIndex: number): void {
    this.histogramSeries.moveToPane(paneIndex);
    this.MMSeries.moveToPane(paneIndex);
    this.signalSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.histogramSeries.getPane().paneIndex()
  }
}
