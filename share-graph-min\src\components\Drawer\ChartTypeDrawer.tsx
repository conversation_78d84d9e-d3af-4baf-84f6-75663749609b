import { useState, useEffect } from 'react';
import { i18n } from '@euroland/libs';
import { useAppStore } from '../../stores/useAppStore';
import { CHART_TYPE_KEYS } from '../../constants/chartConstant';
import { TChartType } from '../../types/store';
import Drawer from './Drawer';
import OptionsList from './OptionsList';

interface ChartTypeDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const ChartTypeDrawer: React.FC<ChartTypeDrawerProps> = ({ isOpen, onClose }) => {
  const chartType = useAppStore((state) => state.chartType);
  const setChartType = useAppStore((state) => state.setChartType);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  // Chart type options from ChartTypeDropdown
  const options = [
    {
      id: CHART_TYPE_KEYS.MOUNTAIN.key,
      label: i18n.translate(CHART_TYPE_KEYS.MOUNTAIN.label),
    },
    {
      id: CHART_TYPE_KEYS.LINE.key,
      label: i18n.translate(CHART_TYPE_KEYS.LINE.label),
    },
    {
      id: CHART_TYPE_KEYS.CANDLESTICK.key,
      label: i18n.translate(CHART_TYPE_KEYS.CANDLESTICK.label),
    },
    {
      id: CHART_TYPE_KEYS.BAR_OHLC.key,
      label: i18n.translate(CHART_TYPE_KEYS.BAR_OHLC.label),
    },
    {
      id: CHART_TYPE_KEYS.BASELINE.key,
      label: i18n.translate(CHART_TYPE_KEYS.BASELINE.label),
    },
    {
      id: CHART_TYPE_KEYS.BASE_MOUNTAIN.key,
      label: i18n.translate(CHART_TYPE_KEYS.BASE_MOUNTAIN.label),
    },
  ];

  // Sync with current chart type from store
  useEffect(() => {
    setSelectedOptions([chartType]);
  }, [chartType]);

  const handleToggleOption = (optionId: string) => {
    // For chart type we only allow one selection
    setSelectedOptions([optionId]);
    setChartType(optionId as TChartType);
  };

  return (
    <Drawer 
      isOpen={isOpen} 
      onClose={onClose} 
      title={i18n.translate('chartType')}
    >
      <OptionsList 
        options={options}
        selectedOptions={selectedOptions}
        onToggleOption={handleToggleOption}
      />
    </Drawer>
  );
};

export default ChartTypeDrawer;