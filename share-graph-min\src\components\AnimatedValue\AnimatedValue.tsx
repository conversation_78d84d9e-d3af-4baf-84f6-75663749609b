import React, { ReactElement, ReactNode } from 'react';
import { usePrevious } from '../../hooks/usePrevious';
import clsx from 'clsx';

interface AnimatedValueProps {
  value: number;
  className?: string;
  children?: ReactNode;
}

const AnimatedValue: React.FC<AnimatedValueProps> = ({ value, children }) => {
  const prevValue = usePrevious(value);
  // const [animate, setAnimate] = useState(false);

  // useEffect(() => {
  //   if (prevValue !== undefined && prevValue !== value) {
  //     setAnimate(true);
  //     const timer = setTimeout(() => setAnimate(false), 100); // Duration should match the CSS animation duration
  //     return () => clearTimeout(timer);
  //   }
  // }, [value, prevValue]);

  if (React.isValidElement(children)) {
    const child = children as ReactElement<{ className?: string }>;
    const combinedClassName = clsx(child.props.className, {
      'value-updated': prevValue !== undefined && prevValue !== value,
      up: prevValue && value > prevValue,
      down: prevValue && value < prevValue,
    });

    return React.cloneElement(child, {
      className: combinedClassName,
    });
  }

  return <>{children}</>;
};

export default AnimatedValue;
