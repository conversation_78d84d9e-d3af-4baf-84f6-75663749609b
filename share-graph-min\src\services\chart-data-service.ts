import dayjs from "dayjs";
import { client } from "../graphql/client";
import { CHART_HISTORICAL_DATA, CHART_INTRADAY_DATA } from "../graphql/queries/chartHistoryQuery";
import { IDataFetch, IDataFetchQuery, IDataFetchUtils } from "../lightweight-chart/advance-chart/data-feed";
import { OHLCVSimple,  } from "../lightweight-chart/interface";
import {IMarket} from "../lightweight-chart/advance-chart/market";
import {groupBy, uniqBy} from "es-toolkit";
import { Period } from "../lightweight-chart/advance-chart/i-advance-chart";
import {dayjsToTime, timeToDate, timeToDayjs} from "../lightweight-chart/helpers/utils";

const convertApiDataToChartData = (data): OHLCVSimple[] => {
    const dataLength = data.length;
    const newData: OHLCVSimple[] = new Array(dataLength);
    for (let index = 0; index < dataLength; index++) {
        const item = data[index];
        const newItem = {
            ...item,
            time: dayjs(item.dateTime).unix(),
        }
        delete newItem.dateTime;
        delete newItem.instrumentId;
        newData[index] = newItem;
    }
    
    return uniqBy(newData, (item) => item.time).map((item) => ({
      ...item,
      open: item.open ?? item.close,
      high: item.high ?? item.close,
      low: item.low ?? item.close,
      volume: item.volume ?? 0
    }));
}

class DataFetch implements IDataFetch {
  browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  constructor(
    protected instrumentId: number, 
    protected marketManager?: IMarket,
    public refeshTime = 1000 * 30
  ) {
  }

  fetchIntraDayData = async (params: IDataFetchQuery, isExtended = false) => {
    const { from, to, interval } = params;

    const getTimeIntervalGrouping = () => {
      switch(interval.period) {
        case Period.minute:
          return interval.times;
        case Period.hour:
          return interval.times * 60;
        default:
          throw new Error('do not support interval' + interval.period)
      }
    }
    const differenceInDays = dayjs(to).diff(dayjs(from), 'day');
    let newFrom;
    if (isExtended && differenceInDays <= 3) {
       newFrom = dayjs(to).subtract(3, 'day')
    }
    const result =  await client.query(CHART_INTRADAY_DATA, {
      id: this.instrumentId,
      fromDate: dayjs(newFrom || from).toISOString(),
      toDate:  dayjs(to).toISOString(),
      timeIntervalGrouping: getTimeIntervalGrouping()
    }) 

    const data = result?.data?.instrumentById?.intraday?.nodes || [];
    const convertedData = convertApiDataToChartData(data);
    const marketManager = this.marketManager
    if(marketManager) {
      const groupByDate = groupBy(convertedData, (item) =>
        marketManager.toMarketDate(timeToDayjs(item.time)).format('YYYY-MM-DD')
      );
      return Object.values(groupByDate)
        .map((dateData) =>
          dateData.filter((item) => marketManager.isOpen(timeToDate(item.time)))
        )
        .flat();
    }

    return convertedData
  }

  fetchHistoricalData = async (params: IDataFetchQuery) => {
    const { from, to } = params;

    const result =  await client.query(CHART_HISTORICAL_DATA, {
      id: this.instrumentId,
      fromDate: dayjs(from).toISOString(),
      toDate:  dayjs(to).toISOString(),
    }) 
    const data = result?.data?.instrumentById?.historicals?.nodes || [];
    return convertApiDataToChartData(data);
  }

  async fetchData(param: IDataFetchQuery, isExtended = false): Promise<OHLCVSimple[]> {
    const { interval } = param;
    const {period} = interval;
    const isIntraday = [Period.minute, Period.hour].includes(period);
   
    const result = isIntraday ? await this.fetchIntraDayData(param, isExtended) : await this.fetchHistoricalData(param);
    return result
  }

  async fetchInitialData(param: IDataFetchQuery): Promise<OHLCVSimple[]> {
    return this.fetchData(param, true)
  }

  async fetchPaginationData(param: IDataFetchQuery): Promise<OHLCVSimple[]> {
    return this.fetchData(param, true)
  }

  async fetchUpdateData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]> {
    const { forward } = utils;
    const { from, to, interval } = param;

    // During realtime updates, some of the last ticks may not aggregate correctly, so we need to fetch 5 ticks before
    const fromDate = forward(dayjsToTime(dayjs(from)), -5).toDate();

    return this.fetchData({ from: fromDate, to: to, interval }, false)
  }
}

export default DataFetch;