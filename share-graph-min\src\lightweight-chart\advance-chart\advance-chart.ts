import {
  AreaSeries,
  BarSeries,
  BaselineSeries,
  CandlestickSeries,
  createChart,
  DeepPartial,
  IChartApi,
  ISeriesApi,
  LastPriceAnimationMode,
  LineSeries,
  Logical,
  PriceScaleMode,
  SeriesType,
  TickMarkFormatter,
} from 'lightweight-charts';
import {
  ChartIndicator,
  downColor,
  upColor,
} from '../indicators/abstract-indicator';
import { OHLCVExtraData, OHLCVSimple } from '../interface';
import { Delegate, IPublicDelegate, ISubscription } from '../helpers/delegate';
import { IndicatorFactory, VolumeIndicator } from '../indicators';
import { binarySearchIndex, timeToDate } from '../helpers/utils';
import { cloneDeep, merge } from 'es-toolkit';
import {NumberFormatterFactory} from '../helpers/number-formatter';
import {VolumeIndicatorOptions} from '../indicators/volume-indicator';
import {IAdvanceChart, IAdvanceChartOptions, IAdvanceChartType, IGroupIndicatorByPane, Interval, Period} from './i-advance-chart';
import {DisplayTimezone} from './DisplayTimezone';
import { DEFAULT_CHART_HEIGHT } from '../../configs/defaultSetting';


export const defaultAdvanceChartOptions: IAdvanceChartOptions = {
  upColor,
  downColor,
  mainColor: '#3594e7',
  highLowLineVisible: false,
  highLineColor: upColor,
  lowLineColor: downColor,
  priceScaleMode: PriceScaleMode.Normal,
  priceLineVisible: false,
  locale: 'en',
  gridColor: '#f2f2f2',
  axesColor: '#333',
  tzDisplay: Intl.DateTimeFormat().resolvedOptions().timeZone,
  height: DEFAULT_CHART_HEIGHT
};


export class AdvanceChart implements IAdvanceChart {
  options: IAdvanceChartOptions;
  chartApi: IChartApi;
  chartType: IAdvanceChartType | null = null;
  mainSeries: ISeriesApi<SeriesType> | null = null;
  dataInterval: Interval = {
    period: Period.day,
    times: 1
  };
  __destroyed = false
  
  private data: OHLCVExtraData[] = [];
  // private dataSet: OHLCVExtraData[] = [];
  private indicators = new Map<string, ChartIndicator>();
  private _volumeType: 'volume' | 'volume_overlay' | undefined = undefined;
  // private _displayTimezone = new DisplayTimezoneUtils()
  

  private _chartTypeChanged = new Delegate();
  private _indicatorChanged = new Delegate<string, 'add' | 'remove'>();
  private _destroyed = new Delegate();
  private _updated = new Delegate();
  private _crosshairMoved = new Delegate<OHLCVExtraData, OHLCVExtraData>();
  private _chartHovered = new Delegate<boolean>();
  private _dataSetChanged = new Delegate<OHLCVExtraData[]>();
  private _loading = new Delegate<boolean>();
  private _optionChanged = new Delegate();
  private _mainSeriesChanged = new Delegate();

  private _displayTimezone: DisplayTimezone

  constructor(
    container: HTMLElement,
    options?: DeepPartial<IAdvanceChartOptions>
  ) {
    this.options = Object.freeze(merge(cloneDeep(defaultAdvanceChartOptions), options ?? {}));
    this.chartApi = createChart(container, {
      layout: {
        attributionLogo: false,
        panes: {
          separatorColor: '#e0e3eb',
          enableResize: false,
        },
        fontSize: this.options.fontSize,
        fontFamily: this.options.fontFamily,
        textColor: this.options.axesColor,
      },
      autoSize: true,
      height: this.options.height,
      localization: {
        locale: this.options.locale,
        percentageFormatter: (percentageValue) => this.numberFormatter.percent(percentageValue/ 100),
        // priceFormatter: (price) => this.numberFormatter.decimal(price),
        timeFormatter: (time) => this._displayTimezone.format(timeToDate(time))
      },
      timeScale: {
        borderVisible: false,
        rightOffset: 10,
        maxBarSpacing: 40,
        minBarSpacing: 4,
        secondsVisible: true,
        timeVisible: true,
        tickMarkFormatter: ((timePoint, tickMarkType) => this._displayTimezone.tickMarkFormatter(timeToDate(timePoint), tickMarkType)) as TickMarkFormatter
      },
      overlayPriceScales: {
        scaleMargins: {
          bottom: 0.05,
          top: 0.05
        }
      },
      leftPriceScale: {
        borderVisible: false,
      },
      handleScale: {
        axisPressedMouseMove: false,
      },
      rightPriceScale: {
        borderVisible: false,
        mode: this.options.priceScaleMode,
      },

      grid: {
        horzLines: {
          visible: false,
        },
        vertLines: {
          color: this.options.gridColor,
        },
      },
    });

    this.chartApi.subscribeCrosshairMove((param) => {
      if (param.time === undefined) return;
      const mainSeries = this.mainSeries;
      if (!mainSeries) return;

      const index = binarySearchIndex(
        this.data,
        param.time,
        (item) => item.time
      );
      if (index === -1) return;

      const [data, prev] = this.getPointFromIndex(index);

      this._crosshairMoved.fire(data, prev);
    });

    this.chartApi.subscribeCrosshairMove((param) => {
      if (param.logical === undefined) return this._chartHovered.fire(false);
      this._chartHovered.fire(true);
    });

    this.chartApi.timeScale().subscribeVisibleTimeRangeChange((param) => {
      if (!param) return this._dataSetChanged.fire([]);
      // this.dataSet = this.getDataSet(param);
      this._dataSetChanged.fire(this.dataSet);
    });

    this._dataSetChanged.subscribe(() => {
      this.tryDrawUpDownLine();
      this.updateBaselineChartType()
    });

    this._displayTimezone = new DisplayTimezone(this)
  }

  get numberFormatter () {
    return NumberFormatterFactory.formatter(this.options.locale ?? 'en')
  }

  get dataSet () {
    const range = this.chartApi.timeScale().getVisibleRange();
    if(!range) return [];
    const { from, to } = range;
    const fromIndex = binarySearchIndex(this.data, from, (item) => item.time);
    const toIndex = binarySearchIndex(this.data, to, (item) => item.time);
    return this.data.slice(fromIndex, toIndex + 1);
  }

  getData() {
    return this.data;
  }

  getIndicators() {
    return Array.from(this.indicators.values());
  }

  getPointFromIndex(index: number) {
    const current = this.data[index];
    const prev = this.data[index > 0 ? index - 1 : index];

    return [current, prev] as const;
  }

  lastPoint() {
    return this.getPointFromIndex(this.data.length - 1);
  }

  setChartType(type: IAdvanceChartType) {
    if (type === this.chartType) return;
    let mainSeries: ISeriesApi<SeriesType>;

    switch (type) {
      case 'line':
        mainSeries = this.chartApi.addSeries(
          LineSeries,
          { 
            color: this.options.mainColor, 
            priceLineVisible: this.options.priceLineVisible,
            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,
            lineWidth: 2
          },
          0
        );
        break;
      case 'candle':
        mainSeries = this.chartApi.addSeries(
          CandlestickSeries,
          { upColor: this.options.upColor, downColor: this.options.downColor, priceLineVisible: this.options.priceLineVisible, },
          0
        );
        break;
      case 'mountain':
        mainSeries = this.chartApi.addSeries(
          AreaSeries,
          {
            topColor: this.options.mainColor,
            lineColor: this.options.mainColor,
            bottomColor: '#ffffff00',
            priceLineVisible: this.options.priceLineVisible,
            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,
            lineWidth: 2
          },
          0
        );
        break;
      case 'bar':
        mainSeries = this.chartApi.addSeries(
          BarSeries,
          { upColor: this.options.upColor, downColor: this.options.downColor, priceLineVisible: this.options.priceLineVisible },
          0
        );
        break;
      case 'baseline':
        mainSeries = this.chartApi.addSeries(
          BaselineSeries,
          {
            topLineColor: this.options.upColor,
            bottomLineColor: this.options.downColor,
            bottomFillColor1: 'transparent',
            bottomFillColor2: 'transparent',
            topFillColor1: 'transparent',
            topFillColor2: 'transparent',
            priceLineVisible: this.options.priceLineVisible,
            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,
            lineWidth: 2
          },
          0
        );
        break;
      case 'base-mountain':
        mainSeries = this.chartApi.addSeries(
          BaselineSeries,
          {
            topLineColor: this.options.upColor,
            bottomLineColor: this.options.downColor,
            priceLineVisible: this.options.priceLineVisible,
            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,
            lineWidth: 2
          },
          0
        );
        break;
      default:
        throw new Error('Invalid chart type');
    }

    if (this.mainSeries) {
      this.chartApi.removeSeries(this.mainSeries);
    }
    this.chartType = type;
    this.mainSeries = mainSeries;
    this.updateBaselineChartType()
    this.applyMainSeriesData();
    this.tryDrawUpDownLine()
    Array.from(this.indicators.values()).forEach(indicator => indicator.mainSeriesChanged?.(mainSeries))
    this._updated.fire();
    this._chartTypeChanged.fire();
  }

  updateBaselineChartType(){
    if (this.mainSeries && (this.chartType === 'baseline' || this.chartType === 'base-mountain')) {
      const baseValue = this.dataSet.at(0)?.close;
      if (baseValue) {
        (this.mainSeries as unknown as ISeriesApi<'Baseline'>).applyOptions({
          baseValue: { price: baseValue },
        });
      }
    }
  }

  setData(data: OHLCVSimple[], interval: Interval) {
    this.dataInterval = interval
    this.data = data.map((item) => ({ ...item, value: item.close, customValues: item }));
    this.applyData();
    const range = this.chartApi.timeScale().getVisibleRange();
    if (range) {
      this._dataSetChanged.fire(this.dataSet);
    }
    this._updated.fire();
  }

  applyMainSeriesData() {
    if (!this.mainSeries) return;
    this.mainSeries.setData(this.data as unknown as OHLCVSimple[]);
  }
  applyData() {
    this.applyMainSeriesData();
    Array.from(this.indicators.values()).map((instance) =>
      instance.setData(this.data)
    );
  }

  update(bar: OHLCVSimple, replaceLastPoint: boolean = false) {
    const [lastPoint] = this.lastPoint()
    const prepareBar = {
      ...bar,
      value: bar.close,
      customValues: bar,
      time: replaceLastPoint ? lastPoint.time : bar.time
    }

    if(replaceLastPoint) {
      this.data.splice(this.data.length - 1, 1, prepareBar)
    } else {
      this.data.push(prepareBar)

      const range = this.chartApi.timeScale().getVisibleLogicalRange();

      // keep current range when add new tick.
      if(range && (Math.floor(range.to) > this.data.length)) {
        const newRange = {from: range.from, to: range.to}
        this.chartApi.timeScale().setVisibleLogicalRange(newRange)
      }
    }

    this.mainSeries?.update(prepareBar as unknown as OHLCVSimple)
    Array.from(this.indicators.values()).map((instance) => instance.update());

    this.tryDrawUpDownLine()
    this._updated.fire();
  }

  tryDrawUpDownLine() {
    if (!this.mainSeries) return;
    if (!this.options.highLowLineVisible) {
      this.mainSeries.priceLines().forEach(line => this.mainSeries?.removePriceLine(line))
      return;
    }
    const dataSet = this.dataSet;
    const low = dataSet.reduce(
      (acc, item) => Math.min(acc, item.close),
      Number.MAX_SAFE_INTEGER
    );
    const high = dataSet.reduce(
      (acc, item) => Math.max(acc, item.close),
      Number.MIN_SAFE_INTEGER
    );
    const [lowPriceLine, highPriceLine] = this.mainSeries?.priceLines() ?? [];

    if (lowPriceLine) {
      lowPriceLine.applyOptions({
        price: low,
        color: this.options.downColor,
      });
    } else {
      this.mainSeries.createPriceLine({
        price: low,
        color: this.options.downColor,
      });
    }
    if (highPriceLine) {
      highPriceLine.applyOptions({
        price: high,
        color: this.options.upColor,
      });
    } else {
      this.mainSeries.createPriceLine({
        price: high,
        color: this.options.upColor,
      });
    }
  }

  applyOptions(options: DeepPartial<IAdvanceChartOptions>) {
    this.options = merge(cloneDeep(this.options), options);
    if (options.highLowLineVisible !== undefined) this.tryDrawUpDownLine();
    if (options.priceScaleMode !== undefined)
      this.chartApi.applyOptions({
        rightPriceScale: { mode: options.priceScaleMode },
      });

    if(options.priceLineVisible !== undefined) this.mainSeries?.applyOptions({priceLineVisible: options.priceLineVisible})

    if(options.locale) this.chartApi.applyOptions({localization: { locale: options.locale }})
  }
  isShowVolume() {
    return Boolean(this._volumeType);
  }

  showVolume(type: typeof this._volumeType = 'volume_overlay', options?: Partial<VolumeIndicatorOptions>) {
    if (!this.mainSeries) return;
    if (!type) return;
    if (this.hasIndicator(type)) return;
    const indicator = this.addIndicator(type) as VolumeIndicator;
    if(options) indicator.applyOptions(options)
    if (this._volumeType !== type) {
      if (this._volumeType) this.removeIndicator(this._volumeType);
      this._volumeType = type;
    }
    const volumePaneIndex = indicator.getPaneIndex();
    if (volumePaneIndex === 0) return;
    const indicatorNeedToMove = Array.from(this.indicators.values()).map(
      (item) => {
        if (item === indicator) return;
        const paneIndex = item.getPaneIndex();
        if (paneIndex === 0) return;
        if (paneIndex >= volumePaneIndex) return;

        return {
          paneIndex,
          indicator: item,
        };
      }
    );
    indicator.setPaneIndex(1);
    
    indicatorNeedToMove.map((item) => {
      if (!item) return;
      item.indicator.setPaneIndex(item.paneIndex + 1);
    });
  }

  hiddenVolume() {
    if (!this._volumeType) return;
    this.removeIndicator(this._volumeType);
  }

  listIndicators() {
    return Array.from(this.indicators.keys()).filter(item => item !== this._volumeType);
  }

  addIndicator(name: string) {
    const instance = IndicatorFactory.createIndicator(
      name,
      this.chartApi,
      {
        numberFormatter: () => this.numberFormatter,
        upColor: this.options.upColor,
        downColor: this.options.downColor,
      },
      this.chartApi.panes().length
    );

    instance.setData(this.data);
    this.indicators.set(name, instance);
    this._updated.fire();
    this._indicatorChanged.fire(name, 'add');
    if(this.mainSeries) {
      instance.mainSeriesChanged?.(this.mainSeries);
    }
    return instance;
  }

  removeIndicator(name: string) {
    const instance = this.indicators.get(name);
    if (!instance) return;
    instance.remove();
    this.indicators.delete(name);
    this._updated.fire();
    this._indicatorChanged.fire(name, 'remove');
  }

  hasIndicator(name: string) {
    return this.indicators.has(name);
  }

  remove() {
    Array.from(this.indicators.values()).map((instance) => instance.remove());
    this.indicators.clear();
    this.chartApi.remove();
    this._destroyed.fire();

    this._chartTypeChanged.destroy();
    this._indicatorChanged.destroy();
    this._destroyed.destroy();
    this._updated.destroy();
    this._crosshairMoved.destroy();
    this._chartHovered.destroy();
    this.__destroyed = true
  }

  groupIndicatorByPane(): Array<IGroupIndicatorByPane> {
    const indicators = Array.from(this.indicators.values());
    const panes = Array.from(this.chartApi.panes()).map((pane) => ({
      pane,
      indicators: [] as ChartIndicator[],
    }));
    for (const indicator of indicators) {
      panes[indicator.getPaneIndex()].indicators.push(indicator);
    }

    return panes;
  }

  fitRange(range: {from: Logical, to: Logical}) {
    const rightOffset = this.chartApi.options().timeScale.rightOffset
    const space = range.to - range.from
    const barVisiable = this.maxBar - rightOffset
    this.chartApi.timeScale().setVisibleLogicalRange({
      from: range.from, 
      to: (space < barVisiable ? range.from + barVisiable : range.to) + rightOffset
    });
  }

  fitContent() {
    const range = {from: 0 as Logical, to: Math.max(this.maxBar, this.data.length) as Logical};
    this.fitRange(range);

    return range
  }

  get maxBar () {
    const maxBarSpacing = this.chartApi.options().timeScale.maxBarSpacing;
    const width = this.chartApi.timeScale().width();

    return Math.round(width / maxBarSpacing)
  }

  get loading () {
    return Boolean(this._loading.lastParams()?.[0])
  }

  set loading(status: boolean) {
    this._loading.fire(status)
  }
  
  /** --------------- public delegate --------------- */

  updated() {
    return this._updated as IPublicDelegate<typeof this._updated>;
  }

  chartTypeChanged() {
    return this._chartTypeChanged as IPublicDelegate<
      typeof this._chartTypeChanged
    >;
  }

  indicatorChanged() {
    return this._indicatorChanged as IPublicDelegate<
      typeof this._indicatorChanged
    >;
  }

  crosshairMoved() {
    return this._crosshairMoved as IPublicDelegate<typeof this._crosshairMoved>;
  }

  destroyed() {
    return this._destroyed as IPublicDelegate<typeof this._destroyed>;
  }

  chartHovered() {
    return this._chartHovered as IPublicDelegate<typeof this._chartHovered>;
  }

  onLoading() {
    return this._loading as IPublicDelegate<typeof this._loading>
  }

  optionChanged(): ISubscription {
    return this._optionChanged as IPublicDelegate<typeof this._optionChanged>
  }

  mainSeriesChanged(): ISubscription {
    return this._mainSeriesChanged as IPublicDelegate<typeof this._mainSeriesChanged>
  }
}
