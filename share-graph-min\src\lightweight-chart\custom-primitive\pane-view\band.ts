import {CanvasRenderingTarget2D} from "fancy-canvas";
import {PrimitivePaneViewBase} from "../primitive-base";
import { Time} from "lightweight-charts";
import {ensureNotNull} from "../../helpers/assertions";
import {binarySearchIndex} from "../../helpers/utils";

export interface BandPrimitivePaneViewOptions {
  backgroundColor: string,
}

export const defaultOptions: BandPrimitivePaneViewOptions = {
  backgroundColor: '#2196f31a',
}

export type BandPrimitiveData = {
  time: Time, 
  upper: number, 
  lower: number
}

export class BandPrimitivePaneView extends PrimitivePaneViewBase<BandPrimitivePaneViewOptions, BandPrimitiveData> {

  dataVisible() {
    const visibleRange = this.getVisibleRange()
    if(!visibleRange) return [];

    const data = this.data;
    let fromIndex = binarySearchIndex(data, visibleRange.from, item => item.time)
    if(fromIndex === -1) fromIndex = 1
    let toIndex = binarySearchIndex(data, visibleRange.to, item => item.time)
    if(toIndex === -1) toIndex = data.length
    return data.slice(fromIndex - 1, toIndex + 2)
  }
  drawBackground(target: CanvasRenderingTarget2D): void {
    const data = this.dataVisible().map((item) => ({
      x: ensureNotNull(this.timeToCoordinate(item.time)),
      upperCoor: ensureNotNull(this.priceToCoordinate(item.upper)),
      lowerCoor: ensureNotNull(this.priceToCoordinate(item.lower)),
    }));

    if(data.length < 2) return;
    
    target.useBitmapCoordinateSpace(scope => {
      const ctx = scope.context;
      ctx.scale(scope.horizontalPixelRatio, scope.verticalPixelRatio);

      ctx.beginPath();
      const region = new Path2D();
      const from = 0;
      const to = data.length
      const first = data[from];
      region.moveTo(
        first.x, 
        first.upperCoor
      );

      for (let i = from + 1; i < to; i++) {
        const point = data[i];
        region.lineTo(
          point.x,
          point.upperCoor
        );
      }

      for (let i = to - 1; i >= from; i--) {
        const point = data[i];
        region.lineTo(point.x, point.lowerCoor);
      }

      region.lineTo(
        first.x, 
        first.lowerCoor
      );
      region.closePath();
      ctx.fillStyle = this.options.backgroundColor
      ctx.fill(region)
    })
  }
  defaultOptions(): BandPrimitivePaneViewOptions {
    return defaultOptions
  }
}