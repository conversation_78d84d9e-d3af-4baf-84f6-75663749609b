import { CHART_INDICATOR_KEYS } from '../../constants/chartConstant';
import OptionList from './OptionList';
import { IOptionList } from '../../types/OptionList';
import { useAppStore } from '../../stores/useAppStore';
import { TChartType } from '../../types/store';
import { i18n } from '@euroland/libs';

const IndicatorOptionList = () => {
  const menuData: IOptionList[] = [
    {
      id: CHART_INDICATOR_KEYS.MACD.key,
      label: CHART_INDICATOR_KEYS.MACD.label,
    },
    {
      id: CHART_INDICATOR_KEYS.RSI.key,
      label: CHART_INDICATOR_KEYS.RSI.label,
    },
    {
      id: CHART_INDICATOR_KEYS.STOCHASTIC.key,
      label: CHART_INDICATOR_KEYS.STOCHASTIC.label,
    },
  ];

  const indicators = useAppStore((state) => state.indicators);
  const setChartIndicators = useAppStore((state) => state.setChartIndicators);

  const handleChangeValue = (optionId: string) => {
    setChartIndicators(optionId as TChartType);

  };

  return (
    <OptionList
      title={i18n.translate("indicator")}
      options={menuData}
      onChange={handleChangeValue}
      value={indicators}
    />
  );
};

export default IndicatorOptionList;
