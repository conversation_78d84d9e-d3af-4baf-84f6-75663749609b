import type {<PERSON><PERSON>hart<PERSON><PERSON>, <PERSON>eries<PERSON>pi, MouseEventParams, OhlcData, SeriesDataItemTypeMap, SeriesType, SingleValueData, Time, WhitespaceData} from "lightweight-charts";
import {cloneDeep, merge} from "es-toolkit";
import {OHLCVData} from "../interface";
import {Delegate, IPublicDelegate} from "../helpers/delegate";
import {binarySearch, timeToDate} from "../helpers/utils";
import {NumberFormatter} from "../helpers/number-formatter";
import {Context, ExecutionContext, IIndicatorBar} from "../helpers/execution-indicator";

export const upColor = '#26a69a'; // Green for bullish candles
export const downColor = '#ef5350'; // Red for bearish candles

export type IndicatorData = OhlcData | SingleValueData

export type InputData = OhlcData | SingleValueData
export type SimpleData = WhitespaceData | SingleValueData

export interface ChartIndicatorOptions {
  overlay: boolean,
  upColor?: string;
  downColor?: string;
  numberFormatter?: () => NumberFormatter
}

export abstract class ChartIndicator <
  IOptions extends ChartIndicatorOptions = ChartIndicatorOptions, 
  IIndicatorData extends readonly number[] = number[],
  IData extends OHLCVData = OHLCVData,
> {
  protected data: Array<IData> | null = null
  options: IOptions;

  mainSeries: ISeriesApi<SeriesType> | null = null

  _dataHovered = new Delegate<IIndicatorBar<IIndicatorData> | undefined>()

  indicatorData: Array<IIndicatorData> = []

  _executionContext: ExecutionContext<IIndicatorData>

  formula(c: Context): IIndicatorData | undefined
  formula(): IIndicatorData | undefined {
    return undefined
  }

  mainSeriesChanged(series: ISeriesApi<SeriesType>) {
    this.mainSeries = series

    this._mainSeriesChanged?.(series)
  } 



  onCrosshairMove(param: MouseEventParams<Time>) {
    if(param.time === undefined) return this._dataHovered.fire(undefined)
    this._dataHovered.fire(this.dataByTime(param.time))
  }

  dataHovered() {
    return this._dataHovered as IPublicDelegate<typeof this._dataHovered>
  }

  constructor(protected chart: IChartApi, options?: Partial<IOptions>) {
    this.options = merge(cloneDeep(this.getDefaultOptions()), options ?? {})
    this.onCrosshairMove = this.onCrosshairMove.bind(this)
    this.chart.subscribeCrosshairMove(this.onCrosshairMove)

    this._executionContext = new ExecutionContext<IIndicatorData>((c) => this.formula?.(c))
  }

  abstract getDefaultOptions(): IOptions

  setData(data: Array<IData>) {
    this.data = data;
    this._executionContext.recalc(data.map(item => ({
      open: item.open, 
      high: item.high, 
      low: item.low, 
      time: Math.floor(timeToDate(item.time).getTime() / 1000), 
      isNew: false, 
      volume: item.volume, 
      close: item.close
    })))
    this.calcIndicatorData();
    this.applyIndicatorData();
  }

  update() {
    if(!this.data) return;
    const lastData = this.data[this.data.length - 1];
    this._executionContext.update({
      open: lastData.open, 
      high: lastData.high, 
      low: lastData.low, 
      time: Math.floor(timeToDate(lastData.time).getTime() / 1000), 
      volume: lastData.volume, 
      close: lastData.close
    })
    this.recalc?.()
    this.applyIndicatorData();
  }

  applyOptions(options: Partial<IOptions>) {
    this.options = merge(this.options, options)
    this._applyOptions?.(options);
  }

  remove() {
    if(this.onCrosshairMove) {
      this.chart.unsubscribeCrosshairMove(this.onCrosshairMove)
    }
  }

  getDataByCrosshair<TSeriesType extends SeriesType, TData extends SeriesDataItemTypeMap<Time>[TSeriesType]>({ logical }: {logical?: number}, series: ISeriesApi<TSeriesType, Time, TData>) {
    if(logical !== undefined) {
      return series.dataByIndex(logical) ?? undefined
    }
  }

  dataByTime(time: Time) {
    return binarySearch(this._executionContext.data, Math.floor(timeToDate(time).getTime() / 1000), item => item.time);
  }

  lastPoint() {
    const data = this._executionContext.data;
    if(data.length === 0) return;
    return data[data.length - 1]
  }

  getData() {
    return this.data
  }

  calcIndicatorData() {}
  recalc?(): void
  applyIndicatorData() {}
  setPaneIndex(paneIndex: number): void
  setPaneIndex() {}

  getPaneIndex() {
    return this.mainSeries?.getPane().paneIndex() ?? 0
  }
  _applyOptions?(options: Partial<IOptions>): void
  _mainSeriesChanged?(series: ISeriesApi<SeriesType>): void
}