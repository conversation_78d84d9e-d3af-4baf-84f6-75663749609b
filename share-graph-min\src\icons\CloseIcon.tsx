import React from "react";
import { ISvgIconProps } from "../types/common";

const CloseIcon: React.FC<ISvgIconProps> = ({
  width = 18,
  height = 18,
  color = "currentColor",
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 18 18"
      width={width}
      height={height}
      className={className}
    >
      <path stroke={color} strokeWidth="1.2" d="m1.5 1.5 15 15m0-15-15 15" />
    </svg>
  );
};

export default CloseIcon;
