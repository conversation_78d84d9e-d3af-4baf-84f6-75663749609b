{"name": "share-graph-mini", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --port 5002 --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "codegen": "gql-gen --config codegen.ts --watch", "test": "vitest", "benchmark": "vitest bench", "tsc": "tsc -b"}, "dependencies": {"@azure/web-pubsub-client": "^1.0.1", "@euroland/libs": "^2.1.2", "@urql/core": "^5.1.0", "base-64": "^1.0.0", "btoa": "^1.2.1", "buffer": "^6.0.3", "clsx": "^2.1.1", "dayjs": "^1.11.13", "es-toolkit": "^1.30.1", "events": "^3.3.0", "fancy-canvas": "^2.1.0", "fast-base64-decode": "^2.0.0", "immer": "^10.1.1", "lightweight-charts": "^5.0.5", "react": "^18.3.1", "react-date-picker": "^11.0.0", "react-dom": "^18.3.1", "technicalindicators": "^3.1.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.15.0", "@graphql-codegen/cli": "^5.0.3", "@graphql-codegen/client-preset": "^4.5.1", "@parcel/watcher": "^2.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "graphql": "^16.10.0", "jest-canvas-mock": "^2.5.2", "jsdom": "^25.0.1", "matchmedia-polyfill": "^0.3.2", "sass": "^1.82.0", "sass-embedded": "^1.82.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1", "vite-plugin-mkcert": "^1.17.6", "vitest": "^2.1.8"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "sass-embedded-linux-x64": "^1.85.1"}}