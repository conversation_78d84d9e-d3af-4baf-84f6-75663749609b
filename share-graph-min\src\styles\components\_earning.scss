$right-price-axis: 58px;
$dividend-color: #346AFF;
$earning-color: #fd8a02;

.event-container {
    position: absolute;
    bottom: 5px;
    left: 0;
    right: 0;
    z-index: 2;
    overflow: hidden;
    height: 100%;
    width: 100%;
    pointer-events: none;
}

.event-marker {
    position: absolute;
    bottom: 0;
    transform: translateX(-50%);
    pointer-events: auto;
    &:hover {
        z-index: 3;
    }

    &__content {
        cursor: pointer;
        background: #fff;
        border-radius: 50%;
        border: none;
        width: 25px;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: 700;
        border: 2px solid;


        &.dividend {
            color: $dividend-color;
            border-color: $dividend-color;
        }

        &.earning {
            color: $earning-color;
            border-color: $earning-color;
        }

        &.active {
            color: #fff;

            &.dividend {
                background: $dividend-color;
            }

            &.earning {
                background: $earning-color;
            }
        }
    }



    &__tooltip {
        position: absolute;
        bottom: calc(100% + 20px);
        left: 50%;
        transform: translateX(-50%);
        background: #fff;
        border: 1px solid $earning-color;
        border-left: 4px solid;
        padding: 16px 16px 16px 12px;
        width: 200px;
        border-radius: 4px;
        overflow: hidden;

        &__icon {
            width: 25px;
            height: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            border: 2px solid;
            border-radius: 50%;
        }

        &.dividend {
            border-color: $dividend-color;

            .event-marker__tooltip__icon {
                color: $dividend-color;
                border-color: $dividend-color;
            }
        }

        &.earning {
            border-color: $earning-color;

            .event-marker__tooltip__icon {
                color: $earning-color;
                border-color: $earning-color;
            }
        }

        &__title {
            font-size: 18px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
    }
}

.earing-marker__title {
    margin-bottom: 10px;
}

.dividend-row {
    &:not(:last-child) {
        margin-bottom: 10px;
    }

    & > span {
        min-width: 50%;
        display: inline-block;
        font-weight: 700;
    }
}