import { afterEach, expect, it, vi } from 'vitest';
import { AdvanceChart } from './advance-chart';
import { render, cleanup } from '@testing-library/react'
import {createElement} from 'react';
import { Time } from 'lightweight-charts';
import {Period} from './i-advance-chart';

// Test helpers for cleaner, reusable test data
const createContainer = () => render(createElement('div')).container;

const createOHLCVData = (additionalItems: Array<{ time: Time; open: number; high: number; low: number; close: number; volume: number }> = []) => [
  { time: 1 as Time, open: 10, high: 15, low: 5, close: 12, volume: 100 },
  { time: 2 as Time, open: 12, high: 18, low: 8, close: 16, volume: 200 },
  ...additionalItems
];

afterEach(() => {
  cleanup()
})

// Core functionality: Chart initialization should provide working chart interface
it('should initialize with working chart API when given container', () => {
  const container = createContainer();
  const chart = new AdvanceChart(container);

  expect(chart.chartApi).toBeDefined();
  expect(chart.loading).toBeDefined();
});

// Core functionality: Chart type changes should be reflected in chart state
it('should update chart type when set to different types', () => {
  const container = createContainer();
  const chart = new AdvanceChart(container);

  chart.setChartType('line');
  expect(chart.chartType).toBe('line');

  chart.setChartType('candle');
  expect(chart.chartType).toBe('candle');

  chart.setChartType('mountain');
  expect(chart.chartType).toBe('mountain');
});

// Core functionality: Setting same chart type multiple times should work without issues
it('should handle setting the same chart type multiple times', () => {
  const container = createContainer();
  const chart = new AdvanceChart(container);

  chart.setChartType('line');
  const firstTypeResult = chart.chartType;

  chart.setChartType('line');
  expect(chart.chartType).toBe(firstTypeResult);
});

// Core functionality: Indicator management should work as expected
it('should add and remove indicators correctly', () => {
  const container = createContainer();
  const chart = new AdvanceChart(container);

  // Initially no indicators
  expect(chart.hasIndicator('volume')).toBe(false);

  // Add indicator
  chart.addIndicator('volume');
  expect(chart.hasIndicator('volume')).toBe(true);

  // Add another indicator
  chart.addIndicator('macd');
  expect(chart.hasIndicator('macd')).toBe(true);
  expect(chart.getIndicators().length).toBe(2);

  // Remove indicator
  chart.removeIndicator('volume');
  expect(chart.hasIndicator('volume')).toBe(false);
  expect(chart.hasIndicator('macd')).toBe(true);
});

// Core functionality: Data setting should update chart data
it('should accept and store OHLCV data correctly', () => {
  const container = createContainer();
  const chart = new AdvanceChart(container);
  const testData = createOHLCVData();

  chart.setData(testData, { period: Period.day, times: 1 });

  const result = chart.getData();
  expect(result).toHaveLength(2);
  expect(result[0]).toMatchObject({
    time: 1,
    open: 10,
    high: 15,
    low: 5,
    close: 12,
    volume: 100,
    value: 12 // close price becomes value
  });
});

// Critical cleanup: Chart removal should clean up resources
it('should clean up all resources when chart is removed', () => {
  const container = createContainer();
  const chart = new AdvanceChart(container);
  
  // Setup some indicators to verify cleanup
  chart.addIndicator('volume');
  chart.addIndicator('macd');
  
  // Subscribe to destruction event to verify it fires
  const destroyCallback = vi.fn();
  chart.destroyed().subscribe(destroyCallback);

  // Remove chart
  chart.remove();

  // Verify cleanup
  expect(chart.getIndicators().length).toBe(0);
  expect(destroyCallback).toHaveBeenCalled();
});
