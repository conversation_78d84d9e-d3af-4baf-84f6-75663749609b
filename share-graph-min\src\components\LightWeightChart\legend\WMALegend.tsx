import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from '../../../lightweight-chart/advance-chart';
import { WMAIndicator } from '../../../lightweight-chart/indicators';

const WMALegend: FC<{
  indicator: WMAIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="WMA"
      indicator={indicator}
      renderer={(d) =>
        d && d.value ? (
          <>
            {' '}
            <span style={{ color: indicator.options.color }}>
              {advanceChart.numberFormatter.decimal(d.value[0])}
            </span>{' '}
          </>
        ) : null
      }
    />
  );
};

export default WMALegend;
