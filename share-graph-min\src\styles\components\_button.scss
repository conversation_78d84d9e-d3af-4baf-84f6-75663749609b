@use "../common/mixins" as *;

.button {
    display: inline-block;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0.5em 1.5em;
    border: 1px solid var(--border-color);
    min-width: 75px;
    background-color: #fff;
    border-radius: 6px;
    height: 34px;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
  
    &--primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      color: #ffffff;
  
      &:hover:not(:disabled) {
        background-color: var(--primary-color);
      }
    }
  
    &--secondary {
      background-color: var(--text-color);
      color: #ffffff;
  
      &:hover:not(:disabled) {
        background-color: var(--text-color);
      }
    }

  
    &:disabled,
    &--disabled {
      background-color: #e0e0e0;
      color: #a0a0a0;
      cursor: not-allowed;
    }

    @include respond-to(sm) {
      padding: 0.5em 1em;
    }
  }
  