import { useState, useEffect } from 'react';
import { i18n } from '@euroland/libs';
import { useAppStore } from '../../stores/useAppStore';
import { CHART_INDICATOR_KEYS } from '../../constants/chartConstant';
import Drawer from './Drawer';
import OptionsList from './OptionsList';

interface IndicatorDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const IndicatorDrawer: React.FC<IndicatorDrawerProps> = ({ isOpen, onClose }) => {
  const indicators = useAppStore((state) => state.indicators);
  const setChartIndicators = useAppStore((state) => state.setChartIndicators);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  // Indicator options from IndicatorDropdown
  const options = [
    {
      id: CHART_INDICATOR_KEYS.MACD.key,
      label: CHART_INDICATOR_KEYS.MACD.label,
    },
    {
      id: CHART_INDICATOR_KEYS.RSI.key,
      label: CHART_INDICATOR_KEYS.RSI.label,
    },
    {
      id: CHART_INDICATOR_KEYS.STOCHASTIC.key,
      label: CHART_INDICATOR_KEYS.STOCHASTIC.label,
    },
    {
      id: CHART_INDICATOR_KEYS.MM.key,
      label: CHART_INDICATOR_KEYS.MM.label,
    },
  ];

  // Sync with current indicators from store
  useEffect(() => {
    setSelectedOptions(indicators);
  }, [indicators]);

  const handleToggleOption = (optionId: string) => {
    // For indicators, we allow only one selection at a time
    setSelectedOptions([optionId]);
    setChartIndicators(optionId);
  };

  return (
    <Drawer 
      isOpen={isOpen} 
      onClose={onClose} 
      title={i18n.translate('indicator')}
    >
      <OptionsList 
        options={options}
        selectedOptions={selectedOptions}
        onToggleOption={handleToggleOption}
      />
    </Drawer>
  );
};

export default IndicatorDrawer;