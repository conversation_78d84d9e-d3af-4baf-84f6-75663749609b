import dayjs, { Dayjs } from "dayjs";
import { RANGE_CHART_INFO, TIME_INTERVALS } from "../constants/chartConstant";
import { IChartRangeStore } from "../types/store";
import { ITimelineOption, TCSSVariables } from "../types/common";
import { IDataFetchQuery } from "../lightweight-chart/advance-chart/data-feed";
import { FONTS } from "../configs/defaultSetting";
import { CACHE_MARKET_INFO_KEY } from "../constants/common";
import { MarketInfo } from "../services/market-service";
import { Period } from "../lightweight-chart/advance-chart/i-advance-chart";
import {IMarket} from "../lightweight-chart/advance-chart/market";

const getPayloadDate = (date: string | Dayjs, type: 'start' | 'end' = 'start'): Date => type === 'start'
    ? dayjs(date).startOf('day').toDate()
    : dayjs(date).endOf('day').toDate();

export const getChartRangeData = (rangeChart: IChartRangeStore, market: IMarket): IDataFetchQuery => {
    const { period, interval, fromDate, toDate } = rangeChart;

    // Calculate the interval and period for predefined range
    if (period !== 'Custom') {
        const periodInfo = RANGE_CHART_INFO[period] as ITimelineOption;
        const { fromDateSubtract } = periodInfo;

        const to = market.isOpen() ? market.getClose(new Date) : market.getPrevClose(new Date);
        const from = market.getClose(to.subtract(fromDateSubtract.number, fromDateSubtract.unit).toDate());
        const intervalByPeriod = TIME_INTERVALS[interval].interval;

        return {
            from: from.toDate(),
            to: to.toDate(),
            interval: intervalByPeriod
        }
    }

    // Calculate the interval and period for custom range
    let newPeriod = Period.day;
    let newTimes = 1;
    const diffFromTwoDates = Math.abs(dayjs(toDate).diff(dayjs(fromDate), 'day'));

    if (diffFromTwoDates <= 7) {
        newPeriod = Period.minute;
    } else if (diffFromTwoDates > 7 && diffFromTwoDates <= 30) {
        newPeriod = Period.minute;
        newTimes = 5;
    }

    return {
        from: getPayloadDate(fromDate as string),
        to: getPayloadDate(toDate as string, 'end'),
        interval: {
            period: newPeriod,
            times: newTimes
        }
    };
}

export const updateCSSVariables = (variables: TCSSVariables): void => {
    const root = document.documentElement;
    Object.entries(variables).forEach(([key, value]) => {
        root.style.setProperty(`--${key}`, value);
    });
};

export const getCssSize = (size: string | number): string => `${size}px`;

export const getFontFamily = (fontFamily: string): string => {
    const font = Object.values(FONTS).find(f => f.key === fontFamily);
    return font ? font.value : FONTS.SYSTEM.value;
}

async function loadFile<T>(filePath: string): Promise<T | null> {
    const result = await (await fetch(import.meta.env.BASE_URL + filePath, {
        headers: {
            "Content-Type": "application/json"
        }
    })).json();

    return result;
}

export async function loadTranslationJson<T>(locale: string): Promise<T | null> {
    try {
        const result = await loadFile<T>(`translations/${locale}.json`);
        return result;
    } catch {
        return await loadFile<T>('translations/en.json')
    }
}

export const getCacheMarketInfoByInsId = (instrumentId: number) => {
    const cache = localStorage.getItem(CACHE_MARKET_INFO_KEY);
    if (!cache) return undefined;
    const marketInfo = JSON.parse(cache);
    return marketInfo[instrumentId];
}

export const setCacheMarketInfo = (instrumentId: number, marketInfo: MarketInfo) => {
    const cache = localStorage.getItem(CACHE_MARKET_INFO_KEY);
    const newCache = cache ? JSON.parse(cache) : {};
    if (newCache[instrumentId]) return;
    newCache[instrumentId] = marketInfo;
    localStorage.setItem(CACHE_MARKET_INFO_KEY, JSON.stringify(newCache));
}

export const isTwoDateAfterOrEqual = ({
    date1,
    date2
}: {
    date1: Dayjs;
    date2: Dayjs;
}): boolean => date1.isAfter(date2) || date1.isSame(date2);
