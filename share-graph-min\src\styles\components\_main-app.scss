@use "../common/mixins" as *;

.main-app {
    height: 100%;
    display: flex;
    gap: 4px;
    background: #E0E3EB;

    @include respond-to(sm) {
        flex-direction: column;
    }
}

.sidebar {
    width: 30%;
    flex-shrink: 0;
    background: #fff;
    position: relative;
    transition: .3s;

    &__settings {
        overflow: auto;
        height: 100%;
        padding: 10px 15px;
        @include style-scroll-bar;
    }

    &__collapsed-icon {
        position: absolute;
        top: 10px;
        left: 0;
        z-index: 2;
        cursor: pointer;
        transform: translateX(-50%) rotate(-90deg);
        transition: .3s;

        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        border-radius: 50%;
        border: none;
        box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
    
        @include respond-to(sm) {
            display: none;
        }
    }

    &__collapsed-menu {
        display: none;

        align-items: center;
        flex-direction: column;
        gap: 10px;
        padding: 15px 0;

        > button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 5px;

            &:hover {
                background: var(--hover-color);
            }
        }
    }

    &.collapsed {
        width: 48px;

        .sidebar {
            &__collapsed-icon {
                transform: translateX(-50%) rotate(90deg);
            }
    
            &__settings {
                display: none;
            }
            &__collapsed-menu {
                display: flex;
            }
        }

        
    }

    @include respond-to(sm) {
        width: 100%;
        height: auto;
    }
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    max-width: 100%;
    
}

.footer {
    &__container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 15px;

        @include respond-to(sm) {
            flex-direction: column;
            gap: 0;
        }
    }
    &__text {
        padding: 8px 16px;
        font-size: 12px;
        color: var(--text-color);
        text-align: right;
    }
}