import dayjs from "dayjs";
import { OHLCVSimple } from "../lightweight-chart/interface";

export const getFromAndToDateByDataFeed = (dataFeed: OHLCVSimple[]): {
    fromDate: Date;
    toDate: Date;
} => {
    const fromDate = dataFeed[0].time as number;
    const toDate = dataFeed[dataFeed.length - 1].time as number;
    return { fromDate: dayjs.unix(fromDate).toDate(), toDate: dayjs.unix(toDate).toDate() };
}