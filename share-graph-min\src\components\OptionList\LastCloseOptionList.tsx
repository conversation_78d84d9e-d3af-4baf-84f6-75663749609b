import { CHART_SETTING_KEYS } from '../../constants/chartConstant';
import OptionList from './OptionList';
import { IOptionList } from '../../types/OptionList';
import { useAppStore } from '../../stores/useAppStore';
import { TChartSetting } from '../../types/store';
import { i18n } from '@euroland/libs';

const LastCloseOptionList = () => {
  const chartSettings = useAppStore((state) => state.chartSettings);
  const setChartSettings = useAppStore((state) => state.setChartSettings);

  const menuData: IOptionList[] = [
    {
      id: CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key,
      label: i18n.translate(CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.label),
      
    },
  ];

  const handleChangeValue = (optionId: string) => {
    setChartSettings(
      CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key,
      optionId as TChartSetting
    );

  };

  return (
    <OptionList
      title={i18n.translate(CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.label)}
      options={menuData}
      onChange={handleChangeValue}
      value={chartSettings[CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key]}
    />
  );
};

export default LastCloseOptionList;
