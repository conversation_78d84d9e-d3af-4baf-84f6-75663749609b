// import { streamSnapshotAction, streamTradesAction } from '../actions/realtimeAction';
// import { getAllInstrumentsSetting, getTickersSetting } from '../configs/configuration-app';

import { WebPubSubClient } from "@azure/web-pubsub-client";
import { useAppStore } from "../stores/useAppStore";

interface TradesData {
  InstrumentID: string;
  hDate: string;
  EnqueuedTime: string;
  hClose: number;
  hSize: number;
  InstrumentType: number;
}

interface StreamSnapshotData {
  InstrumentID: number;
  Bid: number | null;
  BidSize: number | null;
  Ask: number | null;
  AskSize: number | null;
  Last: number | null;
  Date: string;
  High: number | null;
  Low: number | null;
  Open: number | null;
  Volume: number | null;
  TodayTurnover: null;
  EnqueuedTime: string;
}

export interface IRealtimeTradeData {
  id: number;
  volume: number;
  price: number;
  date: string;
}

export interface IRealtimeData {
  id: number;
  bid?: number;
  bidSize?: number;
  ask?: number;
  askSize?: number;
  last?: number;
  date: string;
  high?: number;
  low?: number;
  open?: number;
  volume?: number;
}

function createPubSub(url: string): WebPubSubClient {
  return new WebPubSubClient({
    getClientAccessUrl: async () => {
        const response = await fetch(url); // URL sẽ được chuyển tiếp qua proxy
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const result = await response.json();
      return result
    }
  });
}

async function initRealtimeTrades(groupsTrades : string[]): Promise<void> {
  if (groupsTrades.length === 0) return;

  const {updateRealtimeTickerData, setTrades} = useAppStore.getState();
//   const trades = createPubSub('/api/negotiate?hub=trades');
  const trades = createPubSub(import.meta.env.VITE_TRADE_PUBSUB_URL);

  let lastTradeTime = 0;
  trades.on('group-message', ({ message }) => {
    const data: TradesData = JSON.parse(message.data as string);
    const normalize: IRealtimeTradeData = {
      id: parseInt(data.InstrumentID, 10),
      volume: parseInt(data.hSize.toString(), 10),
      price: parseFloat(data.hClose.toString()),
      date: data.hDate
    };

    const date = new Date(normalize.date);
    const time = date.getTime();
    if(time < lastTradeTime) return;
    lastTradeTime = time;

    updateRealtimeTickerData({
      ...normalize,
      volume: undefined,
      last: normalize.price
    });
    setTrades(normalize)
  });

  await trades.start();
  await Promise.all(groupsTrades.map(item => trades.joinGroup(item)));
}

async function initRealtimeSnapshot(groupsSnapshot : string[]): Promise<void> {
  if (groupsSnapshot.length === 0) return;
  const {updateRealtimeTickerData} = useAppStore.getState();

  const snapshot = createPubSub(import.meta.env.VITE_SNAPSHOT_PUBSUB_URL);

  snapshot.on('group-message', ({ message }) => {
    const data: StreamSnapshotData = JSON.parse(message.data as string);
    const normalize: IRealtimeData = {
      id: data.InstrumentID,
      bid: data.Bid ?? undefined,
      bidSize: data.BidSize ?? undefined,
      ask: data.Ask ?? undefined,
      askSize: data.AskSize ?? undefined,
      date: data.Date,
      high: data.High ?? undefined,
      last: data.Last ?? undefined,
      low: data.Low ?? undefined,
      open: data.Open ?? undefined,
      volume: data.Volume ?? undefined
    }
    updateRealtimeTickerData(normalize);
  });

  await snapshot.start();
  await Promise.all(groupsSnapshot.map(item => snapshot.joinGroup(item)));
}

export default async function initRealtime(realtimeIds: string[]) {
  if (realtimeIds.length === 0) return;
  
  return Promise.all([initRealtimeTrades(realtimeIds.map(String)), initRealtimeSnapshot(realtimeIds.map(String))]);
}
