import { CHART_SETTING_KEYS } from '../../constants/chartConstant';
import OptionList from './OptionList';
import { IOptionList } from '../../types/OptionList';
import { useAppStore } from '../../stores/useAppStore';
import { TChartSetting } from '../../types/store';
import { i18n } from '@euroland/libs';

const VolumeOptionList = () => {
  const unControllerUI = useAppStore((state) => state.appSettings.unControllerUI);
  const chartSettings = useAppStore((state) => state.chartSettings);
  const setChartSettings = useAppStore((state) => state.setChartSettings);

  const disabledVolume = unControllerUI.includes(CHART_SETTING_KEYS.VOLUME.key);
  const isShowVolume = chartSettings.volume.includes(
    CHART_SETTING_KEYS.VOLUME.SHOW_HIDE.key
  );

  const menuData: IOptionList[] = [
    {
      id: CHART_SETTING_KEYS.VOLUME.SHOW_HIDE.key,
      label: i18n.translate(CHART_SETTING_KEYS.VOLUME.SHOW_HIDE.label),
    },
    {
      id: CHART_SETTING_KEYS.VOLUME.UNDERLAY.key,
      label: i18n.translate(CHART_SETTING_KEYS.VOLUME.UNDERLAY.label),
      disabled: !isShowVolume,
    },
    {
      id: CHART_SETTING_KEYS.VOLUME.COLOR_VOLUME_BAR.key,
      label: i18n.translate(CHART_SETTING_KEYS.VOLUME.COLOR_VOLUME_BAR.label),
      disabled: !isShowVolume,
    },
  ];

  const handleChangeValue = (optionId: string) => {
    setChartSettings(CHART_SETTING_KEYS.VOLUME.key, optionId as TChartSetting, );
  };

  if (disabledVolume) {
    return null;
  }

  return (
    <OptionList
      title={i18n.translate('volume')}
      options={menuData}
      onChange={handleChangeValue}
      value={chartSettings[CHART_SETTING_KEYS.VOLUME.key]}
    />
  );
};

export default VolumeOptionList;
