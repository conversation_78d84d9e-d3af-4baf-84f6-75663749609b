import { IDefaultSettingConfigs } from "../types/defaultConfig";

export interface IAppSettingApiResponse {
    companyCode: string;
    version: string;
    chartSetting: IDefaultSettingConfigs;
}



export const getJsonAppSettingApi = async (companyCode: string, v: string = 'vdefault'): Promise<IAppSettingApiResponse> => {
    let settingName = `${companyCode.toLowerCase()}`;
    if (v) settingName = settingName + `/${v}`;
    const response = await fetch(`${import.meta.env.VITE_SETTING_URL}${settingName}.json`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    });
    const data = await response.json();
    return data;
}


