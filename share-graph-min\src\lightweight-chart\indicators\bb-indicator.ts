import {
  ISeriesApi,
  Nominal,
  SeriesType,
  Time,
  WhitespaceData,
} from "lightweight-charts";
import { ChartIndicator, ChartIndicatorOptions } from "./abstract-indicator";
import { BollingerBands } from "technicalindicators";
import {SeriesPrimitiveBase} from "../custom-primitive/primitive-base";
import {BandPrimitivePaneView} from "../custom-primitive/pane-view/band";
import {LinePrimitivePaneView} from "../custom-primitive/pane-view/line";
import {Context, IIndicatorBar} from "../helpers/execution-indicator";
import type {BollingerBandsOutput} from "technicalindicators/declarations/volatility/BollingerBands";

export type UpperBBData = Nominal<number, 'Upper'>
export type MiddleBBData = Nominal<number, 'Middle'>
export type LowerBBData = Nominal<number, 'Lower'>
export type BBData = [UpperBBData, MiddleBBData, LowerBBData]

export interface BBIndicatorOptions extends ChartIndicatorOptions {
  backgroundColor: string;
  upperLineColor: string;
  middleLineColor: string;
  lowerLineColor: string;
  period: number;
  stdDev: number;
};

export const defaultOptions: BBIndicatorOptions = {
  backgroundColor: "#2196f312",
  upperLineColor: "#2196f3",
  middleLineColor: "#ff6d00",
  lowerLineColor: "#2196f3",
  period: 20,
  stdDev: 2,
  overlay: true
};


export type BBIndicatorData = WhitespaceData & {
  middle?: number;
  upper?: number;
  lower?: number;
  pb?: number;
}

export class BBPrimitive extends SeriesPrimitiveBase<BBIndicatorData> {
  bandPaneView: BandPrimitivePaneView;
  upperPaneView: LinePrimitivePaneView;
  middlePaneView: LinePrimitivePaneView;
  lowerPaneView: LinePrimitivePaneView;

  constructor(protected source: BBIndicator) {
    super();
    this.bandPaneView = new BandPrimitivePaneView({
      backgroundColor: this.source.options.backgroundColor,
    });
    this.upperPaneView = new LinePrimitivePaneView({
      lineWidth: 1,
      lineColor: this.source.options.upperLineColor,
    });
    this.middlePaneView = new LinePrimitivePaneView({
      lineWidth: 1,
      lineColor: this.source.options.middleLineColor,
    });
    this.lowerPaneView = new LinePrimitivePaneView({
      lineWidth: 1,
      lineColor: this.source.options.lowerLineColor,
    });
    this._paneViews = [
      this.bandPaneView,
      this.upperPaneView,
      this.middlePaneView,
      this.lowerPaneView
    ];
  }

  update(data: IIndicatorBar<BBData>[]) {
    const definedData: Array<{time: Time, value: BBData}> = [];

    for(const bar of data) {
      const value = bar.value;
      if(!value) continue;
      definedData.push({time: bar.time as Time, value})
    }

    this.bandPaneView.update(
      definedData.map((item) => ({
        time: item.time,
        upper: item.value[0],
        lower: item.value[2],
      }))
    );
    this.upperPaneView.update(
      definedData.map((item) => ({
        time: item.time,
        price: item.value[0],
      }))
    );
    this.middlePaneView.update(
      definedData.map((item) => ({
        time: item.time,
        price: item.value[1],
      }))
    );
    this.lowerPaneView.update(
      definedData.map((item) => ({
        time: item.time,
        price: item.value[2],
      }))
    );
  }
}

export default class BBIndicator extends ChartIndicator<BBIndicatorOptions, BBData> {
  bbPrimitive = new BBPrimitive(this)
  _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {
    series.attachPrimitive(this.bbPrimitive)
  }

  _applyOptions(options: Partial<BBIndicatorOptions>): void {
    if(options.period || options.stdDev) {
      this.applyIndicatorData()
    }
  }

  applyIndicatorData(): void {
      this.bbPrimitive.update(this._executionContext.data)
  }

  formula(c: Context): BBData | undefined {
    const closeSeries = c.new_var(c.symbol.close, this.options.period);
    if(!closeSeries.calculable()) return;

    const result = new BollingerBands({
      values: closeSeries.getAll(),
      period: this.options.period,
      stdDev: this.options.stdDev,
    });

    const item = result.getResult().at(0) as BollingerBandsOutput
    
    return [item.upper as UpperBBData, item.middle as MiddleBBData, item.lower as LowerBBData]
  }

  remove(): void {
    super.remove()
    this.mainSeries?.detachPrimitive(this.bbPrimitive)
  }

  getDefaultOptions(): BBIndicatorOptions {
    return defaultOptions;
  }
}
