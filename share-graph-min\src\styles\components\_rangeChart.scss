@use "../common/mixins" as *;

.chart-range {
    padding: 8px 16px;
    background: #fff;

    &__timeline-options {
        display: flex;
        gap: 4px;
        align-items: center;
        flex-wrap: wrap;
        // overflow-x: auto;
        @include style-scroll-bar;
    }

    &__timeline-option {
        position: relative;



        &:hover .chart-range__interval-buttons {
            display: block;
        }
    }

    &__timeline-button {
        padding: 4px 8px;
        border: none;
        background: none;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            color: var(--primary-color);
        }

        &.active {
            color: var(--primary-color);
            font-weight: 500;
        }
    }

    &__interval-buttons {
        display: none;
        position: absolute;
        bottom: 100%;
        left: 0;
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 999;
        width: max-content;

    }

    &__interval-button {
        display: block;
        width: 100%;
        padding: 6px 12px;
        border: none;
        background: #fff;
        text-align: left;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: var(--hover-color);
        }
        &.active {
            // background: var(--active-color);
            font-weight: 700;
            // color: #fff;
        }
    }
}