import { Nominal } from "lightweight-charts"
import type { Dayjs } from "dayjs"
import dayjs from "dayjs"
import timezone from "dayjs/plugin/timezone"
import utc from "dayjs/plugin/utc"

// Initialize dayjs plugins
dayjs.extend(timezone)
dayjs.extend(utc)

type IMarketDate = Nominal<Dayjs, 'marketDate'>

export interface IMarket {
  isOpen(): boolean
  isOpen(date: Date): boolean
  toMarketDate(date: Dayjs): IMarketDate
  marketZoneNow(): IMarketDate
  getOpen(date: Date): IMarketDate
  getClose(date: Date): IMarketDate
  getNextOpen(date: Date): IMarketDate
  getNextClose(date: Date): IMarketDate
  getPrevOpen(date: Date): IMarketDate
  getPrevClose(date: Date): IMarketDate
}

interface IMarketOptions {
  name: string
  timeZone: string
  open: string
  close: string
}

interface TimeBlock {
  start: string  // HH:mm format
  end: string    // HH:mm format
  isOpen: boolean
}

type MarketDateInput = Dayjs | Date | string

export class Market implements IMarket {
  private parsedOpenTime: { hours: number; minutes: number }
  private parsedCloseTime: { hours: number; minutes: number }

  constructor(protected options: IMarketOptions) {
    this.parsedOpenTime = this.parseTime(this.options.open)
    this.parsedCloseTime = this.parseTime(this.options.close)
  }

  public toMarketDate(date: MarketDateInput): IMarketDate {
    return dayjs(date).tz(this.options.timeZone) as IMarketDate
  }

  private parseTime(timeStr: string): { hours: number; minutes: number } {
    const [hours, minutes] = timeStr.split(':').map(Number)
    return { hours, minutes }
  }

  private getTimeBlocksForDay(): TimeBlock[] {

    const blocks: TimeBlock[] = [{
      start: '00:00',
      end: this.options.open,
      isOpen: false
    }, {
      start: this.options.open,
      end: this.options.close,
      isOpen: true
    }, {
      start: this.options.close,
      end: '23:59',
      isOpen: false
    }]

    return blocks
  }

  private isTimeInRange(time: string, block: TimeBlock): boolean {
    return time >= block.start && time < block.end
  }

  marketZoneNow(): IMarketDate {
    return this.toMarketDate(dayjs())
  }

  private _isOpen(dateInput: IMarketDate): boolean {
    const timeToCheck = dateInput.format('HH:mm')
    const blocks = this.getTimeBlocksForDay()
    return blocks.some(block => this.isTimeInRange(timeToCheck, block) && block.isOpen)
  }

  isOpen(dateInput?: MarketDateInput): boolean {
    return dateInput ? this._isOpen(this.toMarketDate(dateInput)) : this._isOpen(this.marketZoneNow())
  }

  getOpen(dateInput: MarketDateInput): IMarketDate {
    return this.toMarketDate(dateInput)
      .hour(this.parsedOpenTime.hours)
      .minute(this.parsedOpenTime.minutes)
      .second(0) as IMarketDate;
  }

  getClose(dateInput: MarketDateInput): IMarketDate {
    return this.toMarketDate(dateInput)
      .hour(this.parsedCloseTime.hours)
      .minute(this.parsedCloseTime.minutes)
      .second(0) as IMarketDate
  }

  public getNextOpen(dateInput: MarketDateInput): IMarketDate {
    const time = this.getOpen(dateInput);
    const inputDate = this.toMarketDate(dateInput);
    return inputDate.isAfter(time) ? time.add(1, 'day') as IMarketDate : time;
  }

  public getNextClose(dateInput: MarketDateInput): IMarketDate {
    const time = this.getClose(dateInput);
    const inputDate = this.toMarketDate(dateInput);
    return inputDate.isAfter(time) ? time.add(1, 'day') as IMarketDate : time;
  }

  public getPrevOpen(dateInput: MarketDateInput): IMarketDate {
    const time = this.getOpen(dateInput);
    const inputDate = this.toMarketDate(dateInput);
    return inputDate.isBefore(time) ? time.subtract(1, 'day') as IMarketDate : time;
  }

  public getPrevClose(dateInput: MarketDateInput): IMarketDate {
    const time = this.getClose(dateInput);
    const inputDate = this.toMarketDate(dateInput);
    return inputDate.isBefore(time) ? time.subtract(1, 'day') as IMarketDate : time;
  }
}