import { AdvanceChart } from '../../../lightweight-chart/advance-chart';
import {
  BBIndicator,
  EMAIndicator,
  MACDIndicator,
  RSIIndicator,
  SMAIndicator,
  VolumeIndicator,
} from '../../../lightweight-chart/indicators';
import { ChartIndicator } from '../../../lightweight-chart/indicators/abstract-indicator';
import StochasticIndicator from '../../../lightweight-chart/indicators/stochastic-indicator';
import EMALegend from './EMALegend';
import BBLegend from './BBLegend';
import MACDLegend from './MACDLegend';
import RSILegend from './RSILegend';
import SMALegend from './SMALegend';
import StochasticLegend from './StochasticLegend';
import VolumeLegend from './VolumeLegend';

export default function renderLegend(
  indicator: ChartIndicator,
  index: number | string,
  advanceChart: AdvanceChart
) {
  if (indicator instanceof VolumeIndicator)
    return (
      <VolumeLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof RSIIndicator)
    return (
      <RSILegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof MACDIndicator)
    return (
      <MACDLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof BBIndicator)
    return (
      <BBLegend indicator={indicator} advanceChart={advanceChart} key={index} />
    );

  if (indicator instanceof StochasticIndicator)
    return (
      <StochasticLegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if (indicator instanceof SMAIndicator)
    return (
      <SMALegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );

  if(indicator instanceof EMAIndicator)
    return (
      <EMALegend
        indicator={indicator}
        advanceChart={advanceChart}
        key={index}
      />
    );
}
