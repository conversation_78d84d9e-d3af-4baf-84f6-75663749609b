import React from 'react';
import { ISvgIconProps } from '../../types/common';

const BarIcon: React.FC<ISvgIconProps> = ({ width = 28, height = 28, color = 'currentColor', className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 28 28"
      width={width}
      height={height}
      className={className}
      fill={color}
    >
      <path d="M19 6h-1v7h-3v1h3v8h1v-3h3v-1h-3V6ZM11 7h-1v13H7v1h3v2h1V10h3V9h-3V7Z"></path>
    </svg>
  );
};

export default BarIcon;
