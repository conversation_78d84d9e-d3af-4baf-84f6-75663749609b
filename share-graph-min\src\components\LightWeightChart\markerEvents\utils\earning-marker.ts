import { Delegate } from "../../../../lightweight-chart/helpers/delegate";
import { getEarningEvents } from "../../../../services/common";
import { useAppStore } from "../../../../stores/useAppStore";
import { IEarningEvent } from "../../../../types/events";
import { PaginatedLoader, PaginatedResourceHandler } from "./data-loader";


class EarningLoader extends PaginatedLoader<unknown, unknown, IEarningEvent> {
  async fetch(cursor) {
    const { companyCode } = useAppStore.getState().appSettings;

    const result = await getEarningEvents({
      companyCode: companyCode,
      fromDate: this.from,
      toDate: this.to,
      cursor
    });
    const edges = result?.data?.company?.fcEventsByTypes?.edges || [];
    const data = edges.map(item => ({
      dateTime: item.node.dateTime,
      eventName: item.node.eventName,
    } as IEarningEvent))

    // console.log("EarningLoader ~ fetch ~ result:", result)
    return {
      cursor: result?.data?.company?.fcEventsByTypes?.pageInfo?.endCursor || undefined,
      data: data
    }
  }
}

class EarningHandler extends PaginatedResourceHandler<unknown, unknown, IEarningEvent> {
  dataLoaded = new Delegate();

  paginatedLoaderCreator(from, to) {
    return EarningLoader.create<unknown, unknown, IEarningEvent>(from, to, () => {
      this.dataLoaded.fire()
    });
  }
}

// const earningInstance = new EarningHandler(undefined, undefined);

export default EarningHandler;
