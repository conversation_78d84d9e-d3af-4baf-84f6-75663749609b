

import { StateCreator } from "zustand"
import { TStoreState } from "../useAppStore"
import { IDefaultSettingConfigs } from "../../types/defaultConfig";
import { DEFAULT_STORE_CHART_SETTINGS } from "../../constants/chartConstant";
import { TChartSettingsStore } from "../../types/store";
import { getMarketInfoData, MarketInfo } from "../../services/market-service";
import { IMarket, Market } from "../../lightweight-chart/advance-chart/market";
import { NumberFormatter } from "../../lightweight-chart/helpers/number-formatter";
import { setCacheMarketInfo } from "../../utils/common";

export type IMarketInfoStore = MarketInfo & { marketManger: IMarket }

export interface IAppStore {
    marketInfo: IMarketInfoStore | undefined;
    marketInfoCaches: { [key: number]: IMarketInfoStore };
    appSettings: IDefaultSettingConfigs;
    formatNumberInstance: NumberFormatter;
    selectedInstrumentId: number;
    setAppSettings: (appSettings: IDefaultSettingConfigs) => void;
    setMarketInfo: (marketInfo: MarketInfo, insId: number) => void;
    setFormatNumberInstance: (instance: NumberFormatter) => void;
    changeSelectedInstrumentId: (insId: number) => void;
    changeMarketInfo: (insId: number) => void;
}

export const createAppSlice: StateCreator<
    TStoreState,
    [],
    [],
    IAppStore
> = (set, get) => ({
    appSettings: {} as IDefaultSettingConfigs,
    marketInfo: undefined as (IMarketInfoStore | undefined),
    marketInfoCaches: {},
    formatNumberInstance: {} as NumberFormatter,
    selectedInstrumentId: 0,
    setAppSettings: (appSettings) => {
        const { chartConfiguration, defaultRange, dateRangesAndInterval, defaultSelectedInstrumentId } = appSettings;
        const { setChartType, setChartRange, setChartEvents } = get();

        // Update selectedInstrumentId
        set({ selectedInstrumentId: defaultSelectedInstrumentId });

        // Update events
        setChartEvents(appSettings.events)

        // Update chartType
        setChartType(chartConfiguration.chartType)

        // Update chartRange
        const { period: defaultPeriod, interval: defaultInterval } = defaultRange;

        // Find the matching period and check for the interval
        const periodInDateRanges = dateRangesAndInterval.find(({ period }) => period === defaultPeriod);
        const hasValidInterval = periodInDateRanges?.intervals.includes(defaultInterval);

        // Determine the final period and interval
        const period = periodInDateRanges ? defaultPeriod : dateRangesAndInterval[0]?.period;
        const interval = hasValidInterval
            ? defaultInterval
            : periodInDateRanges?.intervals[0] || dateRangesAndInterval[0]?.intervals[0];
        setChartRange({
            period, interval
        })

        // update chartSettings
        const newChartSettings = {} as TChartSettingsStore;
        Object.keys(DEFAULT_STORE_CHART_SETTINGS).forEach((key) => {
            newChartSettings[key] = chartConfiguration[key] || DEFAULT_STORE_CHART_SETTINGS[key];
        });
        set({ chartSettings: newChartSettings });

        // Update appSettings
        set({ appSettings })
    },
    setMarketInfo: (marketInfo, insId) => {
        const { marketInfoCaches } = get();

        const marketInfoCache = marketInfoCaches[insId];
        if (marketInfoCache) {
            set({ marketInfo: marketInfoCache });
            return;
        }

        const newMarketInfo = {
            ...marketInfo,
            marketManger: new Market({
                close: marketInfo.close,
                name: 'sample',
                open: marketInfo.open,
                timeZone: marketInfo.timezone
            })
        };

        set({
            marketInfoCaches: {
                ...marketInfoCaches,
                [insId]: newMarketInfo
            },
            marketInfo: newMarketInfo

        })

    },
    setFormatNumberInstance: (instance) => set({ formatNumberInstance: instance }),
    changeSelectedInstrumentId: (insId) => {
        const { changeMarketInfo } = get();
        changeMarketInfo(insId)

        set({ selectedInstrumentId: insId });

    },
    changeMarketInfo: (insId) => {
        const { tickers, setMarketInfo } = get();
        const market = tickers[insId]?.market;
        if (market) {
            const marketInfo = getMarketInfoData(market)
            setCacheMarketInfo(insId, marketInfo)
            setMarketInfo(marketInfo, insId)
        }

    },

})