import { memo, useEffect, useState } from 'react';
import { useChartContext } from './context';



const IconLoading = ({ color }: { color: string}) => (
  <svg
    style={{ width: 50, color }}
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 200 200"
  >
    <radialGradient
      id="a9"
      cx=".66"
      fx=".66"
      cy=".3125"
      fy=".3125"
      gradientTransform="scale(1.5)"
    >
      <stop offset="0" stopColor="currentColor"></stop>
      <stop offset=".3" stopColor="currentColor" stopOpacity=".9"></stop>
      <stop offset=".6" stopColor="currentColor" stopOpacity=".6"></stop>
      <stop offset=".8" stopColor="currentColor" stopOpacity=".3"></stop>
      <stop offset="1" stopColor="currentColor" stopOpacity="0"></stop>
    </radialGradient>
    <circle
      className='loading-rotating'
      transform-origin="center"
      fill="none"
      stroke="url(#a9)"
      strokeWidth="20"
      strokeLinecap="round"
      strokeDasharray="200 1000"
      strokeDashoffset="0"
      cx="100"
      cy="100"
      r="70"
    />
    <circle
      fill="none"
      opacity=".1"
      stroke="currentColor"
      strokeWidth="20"
      strokeLinecap="round"
      cx="100"
      cy="100"
      r="70"
    ></circle>
  </svg>
);

const Loading = () => {
  const { getChart } = useChartContext();
  const [show, showSet] = useState(false)

  useEffect(() => {
    const chart = getChart()
    if(!chart) return;
    chart.onLoading().subscribe(showSet)
    return () => chart.onLoading().unsubscribe(showSet)
  }, [getChart]);

  return (
    <div
      style={{
        position: 'absolute',
        left: 0,
        right: 0,
        zIndex: 200,
        width: '100%',
        height: '100%',
        backgroundColor: '#eeeeee40',
        display: show ? 'flex' : 'none',
        justifyContent: 'center',
        alignItems: 'center',
        pointerEvents: 'none'
      }}
    >
      <IconLoading color={getChart()?.options.mainColor ?? ''} />
    </div>
  );
};

export default memo(Loading);
