@use "sass:map";

$breakpoints: (
    "xs": 480px,
    "sm": 768px,
    "md": 1024px,
    "lg": 1280px,
    "xl": 1440px
);

@mixin respond-to($breakpoint, $type: "max") {
    $value: map.get($breakpoints, $breakpoint);

    @if $value {
        @if $type =="min" {
            @media (min-width: $value) {
                @content;
            }
        }

        @else if $type =="max" {
            @media (max-width: $value) {
                @content;
            }
        }

        @else {
            @warn "Media type is invalid: #{$type}. Only support  'min' and 'max'.";
        }
    }

    @else {
        @warn "Breakpoint #{$breakpoint} isn't exist in the map $breakpoints.";
    }
}

@mixin style-scroll-bar {
    &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background: var(--text-color);
        border-radius: 10px;
    }

    &::-webkit-scrollbar-corner {
        background: transparent;
    }
}

@keyframes realtime-update-animation {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}


@mixin realtime-update {
    animation: realtime-update-animation 0.5s ease-out;
}