// import Header from './components/Header'
import MainContent from './components/MainContent';
import Sidebar from './components/Sidebar';
import { useAppStore } from './stores/useAppStore';
function App() {
  const settingPosition = useAppStore((state) => state.appSettings.settingPosition);
  return (
    <div className="main-app">
      {/* <Header /> */}
      <MainContent />
      {settingPosition === 'right' && <Sidebar />}
    </div>
  );
}

export default App;
