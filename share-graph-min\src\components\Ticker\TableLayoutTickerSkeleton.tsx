import clsx from 'clsx';

const TableLayoutTickerSkeleton = () => {
  // Create two columns with 4 items each to match the original layout
  const columns = [
    { id: 'column1', itemCount: 4 },
    { id: 'column2', itemCount: 4 },
  ];

  return (
    <div className="ticker-table">
      <div className={clsx("ticker-table__price", 'single')}>
        {/* Instrument radio skeleton */}
        <div className="skeleton-loader w-40 h-6 rounded"></div>
        
        {/* Price skeleton */}
        <p className="ticker-table__quote-price">
          <span className="skeleton-loader w-32 h-7 rounded"></span>
        </p>
        
        {/* Time skeleton */}
        <p className="ticker-table__quote-time">
          <span className="skeleton-loader w-36 h-5 rounded"></span>
        </p>
      </div>
      
      <div className="ticker-table__info">
        {columns.map((column) => (
          <ul className={clsx("ticker-table__column")} key={column.id}>
            {Array.from({ length: column.itemCount }).map((_, index) => (
              <li className="ticker-table__item" key={index}>
                <div className="ticker-table__item__label">
                  <span className="skeleton-loader w-20 h-4 rounded"></span>
                </div>
                <div className="ticker-table__item__content">
                  <span className="skeleton-loader w-24 h-5 rounded"></span>
                </div>
              </li>
            ))}
          </ul>
        ))}
      </div>
    </div>
  );
};

export default TableLayoutTickerSkeleton; 