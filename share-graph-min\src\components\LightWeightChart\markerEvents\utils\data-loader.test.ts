import { beforeEach, describe, expect, it, vi } from 'vitest'
import { PaginatedResourceHandler, PaginatedLoader } from './data-loader'

// Test implementations of abstract classes
class TestResourceHandler extends PaginatedResourceHandler<string, { limit: number }, { id: string; value: number }> {
  public resourceNodeCreator = vi.fn(() => ({
    onLoad: vi.fn(),
    destroy: vi.fn()
  }))

  public mockPaginatedLoaderCreator = vi.fn((from: Date, to: Date) => 
    new TestPaginatedLoader(from, to, this.options, this.stx)
  )

  protected paginatedLoaderCreator(from: Date, to: Date) {
    return this.mockPaginatedLoaderCreator(from, to)
  }
}

class TestPaginatedLoader extends PaginatedLoader<string, { limit: number }, { id: string; value: number }> {
  public mockFetch = vi.fn()

  async fetch(cursor?: string) {
    return this.mockFetch(cursor)
  }
}

type TestData = { id: string; value: number }
type MockLoader = { data: TestData[]; destroy: () => void; forceStop: boolean }

const createTestData = (count: number = 3): TestData[] => 
  Array.from({ length: count }, (_, i) => ({ id: `item-${i}`, value: i * 10 }))

describe('PaginatedResourceHandler', () => {
  let handler: TestResourceHandler
  const mockContext = 'test-context'
  const mockOptions = { limit: 10 }

  beforeEach(() => {
    handler = new TestResourceHandler(mockContext, mockOptions)
  })

  it('should load data for new date range', () => {
    const from = new Date('2024-01-01')
    const to = new Date('2024-01-31')

    handler.load(from, to)

    expect(handler.mockPaginatedLoaderCreator).toHaveBeenCalledWith(from, to)
    expect(handler.loaders).toHaveLength(1)
  })

  it('should extend range and create loaders for gaps', () => {
    // Initial load
    handler.load(new Date('2024-01-15'), new Date('2024-01-20'))
    
    // Load earlier dates (left gap)
    handler.load(new Date('2024-01-01'), new Date('2024-01-25'))

    expect(handler.loaders).toHaveLength(3) // 1 initial + 2 gap loaders (left + right)
    expect(handler.mockPaginatedLoaderCreator).toHaveBeenCalledTimes(3)
  })

  it('should return flattened data from all loaders', () => {
    const loader1Data = createTestData(2)
    const loader2Data = createTestData(1)
    
    // Mock loaders with data
    const mockLoader1: MockLoader = { data: loader1Data, destroy: vi.fn(), forceStop: false }
    const mockLoader2: MockLoader = { data: loader2Data, destroy: vi.fn(), forceStop: false }
    handler.loaders = [mockLoader1, mockLoader2] as TestPaginatedLoader[]

    const result = handler.getData()

    expect(result).toEqual([...loader1Data, ...loader2Data])
  })

  it('should clean up resources when sleeping', () => {
    const mockLoader: MockLoader = { data: [], destroy: vi.fn(), forceStop: false }
    const mockDataNode = { destroy: vi.fn(), onLoad: vi.fn() }
    
    handler.loaders = [mockLoader] as TestPaginatedLoader[]
    handler['dataNode'] = mockDataNode

    handler.sleep()

    expect(mockLoader.destroy).toHaveBeenCalled()
    expect(mockDataNode.destroy).toHaveBeenCalled()
  })

  it('should reset all data and loaders', () => {
    const mockLoader: MockLoader = { data: [], destroy: vi.fn(), forceStop: false }
    const mockDataNode = { destroy: vi.fn(), onLoad: vi.fn() }
    
    // Setup initial state
    handler.loaders = [mockLoader] as TestPaginatedLoader[]
    handler['dataNode'] = mockDataNode
    handler['range'] = [new Date('2024-01-01'), new Date('2024-01-31')]

    handler.reset()

    expect(mockLoader.destroy).toHaveBeenCalled()
    expect(mockDataNode.destroy).toHaveBeenCalled()
    expect(handler.loaders).toHaveLength(0)
    expect(handler['range']).toBeUndefined()
    expect(handler['isSleep']).toBe(false)
  })

  it('should detect intersection between date ranges', () => {
    // Set up initial range
    handler['range'] = [new Date('2024-01-15'), new Date('2024-01-25')]

    // Test intersecting ranges
    expect(handler.isIntersectionTime(new Date('2024-01-20'), new Date('2024-01-30'))).toBe(true) // Overlaps end
    expect(handler.isIntersectionTime(new Date('2024-01-10'), new Date('2024-01-20'))).toBe(true) // Overlaps start
    expect(handler.isIntersectionTime(new Date('2024-01-12'), new Date('2024-01-18'))).toBe(true) // Contained within
    expect(handler.isIntersectionTime(new Date('2024-01-10'), new Date('2024-01-30'))).toBe(true) // Contains range

    // Test non-intersecting ranges
    expect(handler.isIntersectionTime(new Date('2024-01-01'), new Date('2024-01-14'))).toBe(false) // Before
    expect(handler.isIntersectionTime(new Date('2024-01-26'), new Date('2024-01-31'))).toBe(false) // After

    // Test with no existing range
    handler['range'] = undefined
    expect(handler.isIntersectionTime(new Date('2024-01-01'), new Date('2024-01-31'))).toBe(false)
  })

  it('should reset when loading non-intersecting range', () => {
    const resetSpy = vi.spyOn(handler, 'reset')
    
    // Initial load
    handler.load(new Date('2024-01-15'), new Date('2024-01-20'))
    expect(resetSpy).not.toHaveBeenCalled()
    
    // Load non-intersecting range (should trigger reset)
    handler.load(new Date('2024-02-01'), new Date('2024-02-15'))
    expect(resetSpy).toHaveBeenCalledOnce()
  })
})

describe('PaginatedLoader', () => {
  let loader: TestPaginatedLoader
  const from = new Date('2024-01-01')
  const to = new Date('2024-01-31')
  const options = { limit: 10 }
  const context = 'test-context'

  beforeEach(() => {
    loader = new TestPaginatedLoader(from, to, options, context)
  })

  it('should load paginated data and call onLoad for each batch', async () => {
    const testData = createTestData(3)
    const onLoad = vi.fn()
    
    loader.mockFetch
      .mockResolvedValueOnce({ data: testData.slice(0, 2), endCursor: 'cursor1' })
      .mockResolvedValueOnce({ data: testData.slice(2), endCursor: undefined })

    await loader.load(onLoad)

    expect(onLoad).toHaveBeenCalledTimes(2)
    expect(onLoad).toHaveBeenNthCalledWith(1, testData.slice(0, 2))
    expect(onLoad).toHaveBeenNthCalledWith(2, testData.slice(2))
    expect(loader.data).toEqual(testData)
  })

  it('should stop loading when no cursor is returned', async () => {
    const onLoad = vi.fn()
    loader.mockFetch.mockResolvedValueOnce({ data: createTestData(1) })

    await loader.load(onLoad)

    expect(loader.mockFetch).toHaveBeenCalledTimes(1)
    expect(onLoad).toHaveBeenCalledTimes(1)
  })

  it('should handle load cancellation', async () => {
    const onLoad = vi.fn()
    loader.mockFetch.mockImplementation(() => 
      new Promise((resolve) => 
        setTimeout(() => resolve({ data: createTestData(1) }), 100)
      )
    )

    const loadPromise = loader.load(onLoad)
    loader.cancel()

    await expect(loadPromise).resolves.toBeUndefined()
    expect(loader.forceStop).toBe(true)
  })

  it('should create and start loading via static method', () => {
    const onLoad = vi.fn()
    const mockLoad = vi.spyOn(TestPaginatedLoader.prototype, 'load').mockResolvedValue()

    TestPaginatedLoader.create(from, to, onLoad, options, context)

    expect(mockLoad).toHaveBeenCalledWith(onLoad)
  })
}) 