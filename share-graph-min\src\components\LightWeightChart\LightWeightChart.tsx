import { ChartProvider } from './context';
import Settings from './Settings';
import PanesRenderer from './PanesRenderer';

import Loading from './Loading';
import {useAppStore} from '../../stores/useAppStore';
import {useMemo} from 'react';
import {DeepPartial} from 'lightweight-charts';
import {getFontFamily} from '../../utils/common';
import renderLegend from './legend/renderLegend';
import GoHome from './GoHome';
import DataFetch from '../../services/chart-data-service';
import MarkerEvents from './markerEvents/MarkerEvents';
import NoData from './NoData';
import {IAdvanceChartOptions} from '../../lightweight-chart/advance-chart/i-advance-chart';
import RealtimeUpdate from './RealtimeUpdate';
import TopSettingChart from './TopSettingChart';
import { DEFAULT_CHART_HEIGHT } from '../../configs/defaultSetting';
import {Log, LogManager} from '../../lightweight-chart/helpers/log';

LogManager.setLevel(Log.DEBUG)

export default function LightWeightChart() {
  const state = useAppStore.getState()
  const selectedInstrumentId =  state.selectedInstrumentId
  const marketInfo = state.marketInfo
  const refreshTickerTime = state.appSettings.tickerSettings.refreshTickerTime

  const chartOptions = useMemo(() => {
    const appSettings = state.appSettings
    const chartConfiguration = appSettings.chartConfiguration;
    const general = appSettings.general;
    return {
      fontSize: parseInt(chartConfiguration.axesFontsize),
      axesColor: chartConfiguration.axesColor,
      gridColor: chartConfiguration.gridColor,
      locale: general.locale,
      downColor: general.downColor,
      upColor: general.upColor,
      mainColor: general.primaryColor,
      fontFamily: getFontFamily(general.fontFamily),
      tzDisplay: marketInfo?.timezone,
      height: DEFAULT_CHART_HEIGHT
    } satisfies DeepPartial<IAdvanceChartOptions>
  }, [marketInfo])

  const instanceDataFetch = useMemo(() => new DataFetch(
    selectedInstrumentId, 
    marketInfo?.marketManger, 
    refreshTickerTime ? refreshTickerTime * 1000 : 0
  ), [selectedInstrumentId, marketInfo, refreshTickerTime])

  return (
    <ChartProvider instanceDataFetch={instanceDataFetch} options={chartOptions}>
      <RealtimeUpdate />
      <Loading />
      <NoData />
      <PanesRenderer
        render={({ pane, indicators }, advanceChart) => (
          <>
            {pane.paneIndex() === 0 ? <GoHome /> : null}
            <div className="chart-legends">
              {pane.paneIndex() === 0 ? <TopSettingChart /> : null}
              <div className="chart-legends__indicators">{indicators.map((item, index) => renderLegend(item, index, advanceChart))}</div>
            </div>
            {pane.paneIndex() === 0 ? <MarkerEvents /> : null}

          </>
        )}
      />
      <Settings />
    </ChartProvider>
  );
}
