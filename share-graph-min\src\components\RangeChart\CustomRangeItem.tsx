import React, { useState } from 'react';
import CustomRangeChartModal from '../Modal/CustomRangeChartModal';
import { i18n } from '@euroland/libs';
import { TChartRangePeriod } from '../../types/store';
import { useAppStore } from '../../stores/useAppStore';

interface CustomRangeItemProps {
  period: TChartRangePeriod;
}

const CustomRangeItem: React.FC<CustomRangeItemProps> = ({ period }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
    const customRange = useAppStore((state) => state.appSettings.customRange);
  

  const handleOpenCustomModal = (visible: boolean) => {
    setIsModalOpen(visible);
  };

  if (!customRange) {
    return null;
  }

  return (
    <>
      <li className="chart-range__timeline-option">
        <button
          className={`chart-range__timeline-button ${
            period === 'Custom' ? 'active' : ''
          }`}
          onClick={() => handleOpenCustomModal(true)}
        >
          {i18n.translate('custom')}
        </button>
      </li>

      <CustomRangeChartModal
        visible={isModalOpen}
        onClose={() => handleOpenCustomModal(false)}
      />
    </>
  );
};

export default CustomRangeItem;
