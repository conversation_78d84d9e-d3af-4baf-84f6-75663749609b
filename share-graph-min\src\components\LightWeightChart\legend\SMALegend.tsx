import { FC } from 'react';
import { Legend } from './Legend';
import { AdvanceChart } from '../../../lightweight-chart/advance-chart';
import { SMAIndicator } from '../../../lightweight-chart/indicators';

const SMALegend: FC<{
  indicator: SMAIndicator;
  advanceChart: AdvanceChart;
}> = ({ indicator, advanceChart }) => {
  return (
    <Legend
      name="SMA"
      indicator={indicator}
      renderer={(d) =>
        d ? (
          <>
            {' '}
            <span style={{ color: indicator.options.color }}>
              {advanceChart.numberFormatter.decimal(d.value.at(0))}
            </span>{' '}
          </>
        ) : null
      }
    />
  );
};

export default SMALegend;
