import { ISvgIconProps } from "../types/common"

const OverlayIcon: React.FC<ISvgIconProps> = ({ width = 16, height = 16 }) => {
  return (
    <svg
  xmlns="http://www.w3.org/2000/svg"
  width={24}
  height={24}
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  strokeWidth={2}
  strokeLinecap="round"
  strokeLinejoin="round"
  style={{color: 'currentColor', width: width, height: height}}
>
  <path d="M3 3v18h18" />
  <path d="m19 9-5 5-4-4-3 3" />
</svg>

  )
}

export default OverlayIcon