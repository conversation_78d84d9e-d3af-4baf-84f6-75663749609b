import {DataChangedScope, DeepPartial, IChartApi, IPrimitivePaneRenderer, IPrimitivePaneView, ISeriesApi, ISeriesPrimitive, SeriesAttachedParameter, SeriesType, SingleValueData, Time, OhlcData, LogicalRange, Logical, WhitespaceData, ITimeScaleApi} from "lightweight-charts";
import {ensureDefined, ensureNotNull} from "../helpers/assertions";
import {BitmapCoordinatesRenderingScope, CanvasRenderingTarget2D} from "fancy-canvas";
import {cloneDeep, merge} from "es-toolkit";
import {binarySearch} from "../helpers/utils";

export interface OHLCVData extends OhlcData {
  volume: number
}

export abstract class SeriesPrimitiveBase<TData extends WhitespaceData = WhitespaceData> implements ISeriesPrimitive<Time> {
  private _chart: IChartApi | undefined = undefined;
  private _series: ISeriesApi<SeriesType> | undefined = undefined;
  protected _paneViews: IPrimitivePaneViewApi[] = []
  indicatorData: TData[] = []
  protected _isDetached = false

  updateAllViews(): void { 
    if(this._isDetached) return;
    this._updateAllViews?.()
  }

  _updateAllViews?() { }

  paneViews(): readonly IPrimitivePaneView[] {
    return this._paneViews
  }

  protected dataUpdated?(scope: DataChangedScope): void;
  protected requestUpdate(): void {
    if (this._requestUpdate) this._requestUpdate();
  }
  private _requestUpdate?: () => void;

  public attached({ chart, series, requestUpdate }: SeriesAttachedParameter<Time>) {
    this._chart = chart;
    this._series = series;
    this._series.subscribeDataChanged(this._fireDataUpdated);
    this._requestUpdate = requestUpdate;
    this.requestUpdate();
    this._paneViews.map(item => item.attached(series, chart))

    this._attached?.()
  }

  _attached?(): void

  get data () {
    const data = this.series.data();
    if(data.length > 0) {
      ensureDefined(data[0].customValues)
    } else {
      return []
    }
    return data.map(item => item.customValues as unknown as OHLCVData)
  }

  public detached() {
    this._series?.unsubscribeDataChanged(this._fireDataUpdated);
    this._chart = undefined;
    this._series = undefined;
    this._paneViews = []
    this._requestUpdate = undefined;
    this._isDetached = true
  }

  public get chart(): IChartApi {
    return ensureDefined(this._chart);
  }

  public get series(): ISeriesApi<SeriesType> {
    return ensureDefined(this._series);
  }

  // This method is a class property to maintain the
  // lexical 'this' scope (due to the use of the arrow function)
  // and to ensure its reference stays the same, so we can unsubscribe later.
  private _fireDataUpdated = (scope: DataChangedScope) => {
    if (this.dataUpdated) {
      this.dataUpdated(scope);
    }
  }

  dataByTime(time: Time): TData | undefined {
    return binarySearch(this.indicatorData, time, item => item.time)
  }

  lastPoint(): TData | undefined {
    return this.indicatorData.at(-1)
  }
}

export interface IPrimitivePaneViewApi extends IPrimitivePaneView, IPrimitivePaneRenderer {
  attached(series: ISeriesApi<SeriesType>, chartApi: IChartApi): void
}

export abstract class PrimitivePaneViewBase <TOptions extends object = object, TData = SingleValueData> implements IPrimitivePaneViewApi {
  options: TOptions

  _series: ISeriesApi<SeriesType> | null = null
  _chartApi: IChartApi | null = null
  _data: TData[] | null = null

  constructor(options?: DeepPartial<TOptions>) {
    this.options = merge(cloneDeep(this.defaultOptions()), options ?? {})
  }

  renderer(): IPrimitivePaneRenderer | null {
    return this
  }

  draw(target: CanvasRenderingTarget2D) {
    if(!this._chartApi) return;
    if(!this._series) return;

    target.useBitmapCoordinateSpace(scope =>
      this._drawImpl?.(scope)
    );
  }

  drawBackground(target: CanvasRenderingTarget2D) {
    if(!this._chartApi) return;
    if(!this._series) return;

    target.useBitmapCoordinateSpace(scope =>
      this._drawBackgroundImpl?.(scope)
    );
  }

  get data() {
    return ensureNotNull(this._data)
  }


  get series() {
    return ensureNotNull(this._series)
  }

  get chartApi() {
    return ensureNotNull(this._chartApi)
  }

  _timeScale: ITimeScaleApi<Time> | undefined
  get timeScale() {
    if(!this._timeScale) this._timeScale = this.chartApi.timeScale();
    return this._timeScale
  }

  getVisibleLogicalRange () {
    const range = this.timeScale.getVisibleLogicalRange()
    if(!range) return;
    return {
      from: Math.floor(range.from) as Logical,
      to: Math.ceil(range.to) as Logical
    } satisfies LogicalRange
  }

  getVisibleRange () {
    return this.timeScale.getVisibleRange()
  }

  coordinateToPrice (coordinate: number) {
    return this.series.coordinateToPrice(coordinate)
  }
  priceToCoordinate (price: number) {
    return this.series.priceToCoordinate(price)
  }

  timeToCoordinate (time: Time) {
    return this.timeScale.timeToCoordinate(time)
  }

  attached(series: ISeriesApi<SeriesType>, chartApi: IChartApi) {
    this._series = series;
    this._chartApi = chartApi
  }

  update(data: TData[], ) {
    this._data = data;
    this._update?.()
  }

  _update?(): void
  _drawImpl?(renderingScope: BitmapCoordinatesRenderingScope): void
  _drawBackgroundImpl?(renderingScope: BitmapCoordinatesRenderingScope): void

  abstract defaultOptions(): TOptions
}