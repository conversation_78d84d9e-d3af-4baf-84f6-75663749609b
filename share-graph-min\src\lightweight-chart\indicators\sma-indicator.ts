import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from "lightweight-charts";
import {SMA} from "technicalindicators";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {SeriesPrimitiveBase} from "../custom-primitive/primitive-base";
import {LineData, LinePrimitivePaneView} from "../custom-primitive/pane-view/line";
import {Context, IIndicatorBar} from "../helpers/execution-indicator";

export interface SMAIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  period: number
}

export const defaultOptions: SMAIndicatorOptions = {
  color: "#2962ff",
  period: 9,
  overlay: true
}

export class SMAPrimitive extends SeriesPrimitiveBase<
  SingleValueData | WhitespaceData
> {
  linePrimitive: LinePrimitivePaneView;
  constructor(protected source: SMAIndicator) {
    super();
    this.linePrimitive = new LinePrimitivePaneView({
      lineColor: this.source.options.color,
    });
    this._paneViews = [this.linePrimitive];
  }

  update(indicatorBars: IIndicatorBar<SMAData>[]) {
    const lineData: LineData[] = []
    for(const bar of indicatorBars) {
      const value = bar.value
      if(value) lineData.push({time: bar.time as Time, price: value[0]})
    }

    this.linePrimitive.update(lineData);
  }
}

export type SMAData = readonly [Nominal<number, 'SMA'>]

export default class SMAIndicator extends ChartIndicator<SMAIndicatorOptions, SMAData> {
  smaPrimitive = new SMAPrimitive(this)
  getDefaultOptions(): SMAIndicatorOptions {
    return defaultOptions
  }

  _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {
    series.attachPrimitive(this.smaPrimitive)
  }

  remove(): void {
    super.remove();
    this.mainSeries?.detachPrimitive(this.smaPrimitive)
  }

  applyIndicatorData(): void {
    this.smaPrimitive.update(
      this._executionContext.data
    )
  }

  formula(c: Context) {
    const smaSeries = c.new_var(c.symbol.close, this.options.period)
    if(!smaSeries.calculable()) return;
    const sma = new SMA({
      values: smaSeries.getAll(),
      period: this.options.period
    });

    return sma.getResult()
  }
}