import {
  ISeriesApi,
  ITimeScaleApi,
  Logical,
  SeriesType,
  Time,
} from 'lightweight-charts';
import {
  timeToDayjs,
} from '../../../../lightweight-chart/helpers/utils';

export function findNearestPosition(
  time: Time,
  timeScale: ITimeScaleApi<Time>,
  mainSeries: ISeriesApi<SeriesType>
) {
  const dateTimeDayjs = timeToDayjs(time).tz('UTC');
  const dateTime = dateTimeDayjs.unix();
  let index = timeScale.timeToIndex(dateTime as Time);
  if (index === null) {
    index = timeScale.timeToIndex(dateTime as Time, true);
    if (index === null) {
      return;
    } else {
      const data = mainSeries.dataByIndex(index);
      if (!data) return;
      const timeInIndex = timeToDayjs(data.time).tz('UTC');

      if(!timeInIndex.isSame(dateTimeDayjs, 'month') || !timeInIndex.isSame(dateTimeDayjs, 'date')) {
        index--;
      }
    }

    if (index < 0) return;
  }

  const xPosi = timeScale.logicalToCoordinate(index as unknown as Logical);
  if (xPosi === null) return;

  return xPosi
}
