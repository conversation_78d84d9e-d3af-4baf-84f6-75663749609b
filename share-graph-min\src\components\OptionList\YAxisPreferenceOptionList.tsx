import { CHART_SETTING_KEYS } from '../../constants/chartConstant';
import OptionList from './OptionList';
import { IOptionList } from '../../types/OptionList';
import { useAppStore } from '../../stores/useAppStore';
import { TChartSetting } from '../../types/store';
import { i18n } from '@euroland/libs';

const YAxisPreferenceOptionList =() => {
  const unControllerUI = useAppStore((state) => state.appSettings.unControllerUI);
  const chartSettings = useAppStore((state) => state.chartSettings);
  const setChartSettings = useAppStore((state) => state.setChartSettings);
  
  const disableYAxisPreference = unControllerUI.includes(CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.key);

  const menuData: IOptionList[] = [
    {
      id: CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.LINEAR.key,
      label: i18n.translate(CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.LINEAR.label),
    },
    {
      id: CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.LOG_SCALE.key,
      label: i18n.translate(CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.LOG_SCALE.label),
    },
    {
      id: CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.PERCENTAGE_VIEW.key,
      label: i18n.translate(CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.PERCENTAGE_VIEW.label),
    },
  ];

  const handleChangeValue = (optionId: string) => {
    setChartSettings(CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.key, optionId as TChartSetting, );
  };

  if (disableYAxisPreference) {
    return null;
  }

  return (
    <OptionList
      title={i18n.translate(CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.label)}
      options={menuData}
      onChange={handleChangeValue}
      value={chartSettings[CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.key]}
    />
  );
};

export default YAxisPreferenceOptionList;
