import Dropdown from './Dropdown';
import { DropdownMenuItem, OnChangeDropdown } from '../../types/common';
import { useAppStore } from '../../stores/useAppStore';
import { CHART_KEYS } from '../../constants/common';
import CheckboxIcon from '../../icons/CheckboxIcon';
import { CHART_EVENT_KEYS } from '../../constants/chartConstant';
import { useMemo } from 'react';

const EventDropdown = () => {
  const enableEvents = useAppStore((state) => state.enableEvents);
  const setChartEvents = useAppStore((state) => state.setChartEvents);

  const eventValue = useMemo(() => {
    const value: string[] = [];
    Object.keys(enableEvents).forEach((key) => {
      if (enableEvents[key]) {
        value.push(key);
      }
    });
    return value;
  }, [enableEvents]);

  const value = {
    [CHART_KEYS.EVENTS]: eventValue,
  };

  const menuData: DropdownMenuItem[] = [
    {
      id: CHART_EVENT_KEYS.DIVIDEND.key,
      label: CHART_EVENT_KEYS.DIVIDEND.label,
      icon: <CheckboxIcon />,
      activeIcon: <CheckboxIcon active />,
    },
    {
      id: CHART_EVENT_KEYS.EARNING.key,
      label: CHART_EVENT_KEYS.EARNING.label,
      icon: <CheckboxIcon />,
      activeIcon: <CheckboxIcon active />,
    },
  ];

  const handleChange: OnChangeDropdown = (val) => {
    setChartEvents(val);
  };

  return (
    <Dropdown
      menuData={menuData}
      onChange={handleChange}
      value={value}
      parentIdFromProp={CHART_KEYS.EVENTS}
      placeholder={
        <>
          <span>Events</span>
        </>
      }
    />
  );
};

export default EventDropdown;
