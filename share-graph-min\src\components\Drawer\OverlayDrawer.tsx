import { useState, useEffect } from 'react';
import { i18n } from '@euroland/libs';
import { useAppStore } from '../../stores/useAppStore';
import { CHART_INDICATOR_KEYS } from '../../constants/chartConstant';
import Drawer from './Drawer';
import OptionsList from './OptionsList';

interface OverlayDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const OverlayDrawer: React.FC<OverlayDrawerProps> = ({ isOpen, onClose }) => {
  const overlays = useAppStore((state) => state.overlays);
  const setChartOverlays = useAppStore((state) => state.setChartOverlays);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  // Overlay options
  const options = [
    {
      id: CHART_INDICATOR_KEYS.SMA.key,
      label: CHART_INDICATOR_KEYS.SMA.label,
    },
    {
      id: CHART_INDICATOR_KEYS.BOLLINGER_BANDS.key,
      label: CHART_INDICATOR_KEYS.BOLLINGER_BANDS.label,
    },
    {
      id: CHART_INDICATOR_KEYS.EMA.key,
      label: CHART_INDICATOR_KEYS.EMA.label,
    },
    {
      id: CHART_INDICATOR_KEYS.SMA.key,
      label: CHART_INDICATOR_KEYS.SMA.label,
    },
  ];

  // Sync with current overlays from store
  useEffect(() => {
    setSelectedOptions(overlays);
  }, [overlays]);

  const handleToggleOption = (optionId: string) => {
    // For overlays, we only allow one selection at a time based on the API
    setSelectedOptions([optionId]);
    setChartOverlays(optionId);
  };

  return (
    <Drawer 
      isOpen={isOpen} 
      onClose={onClose} 
      title={i18n.translate('overlays')}
    >
      <OptionsList 
        options={options}
        selectedOptions={selectedOptions}
        onToggleOption={handleToggleOption}
      />
    </Drawer>
  );
};

export default OverlayDrawer;