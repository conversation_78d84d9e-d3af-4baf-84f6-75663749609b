import {AutoscaleInfoProvider, Time} from "lightweight-charts";
import dayjs from './dayjs-setup'
import { Dayjs } from "dayjs";
export function timeToDate(time: Time) {
  if(typeof time === 'number') return new Date(time * 1000);
  if(typeof time === 'string') return new Date(time);
  if(typeof time === 'object') {
    return new Date(time.year, time.month - 1, time.day)
  }

  throw new Error('Do not support time')
}

export function timeToDayjs(time: Time) {
  return dayjs(timeToDate(time))
} 

export function dayjsToTime(djs: Dayjs): Time {
  return Math.floor(djs.toDate().getTime() / 1000) as Time
}

export const defaultCompare = (a, b) => a === b ? 0 : a < b ? -1 : 1
export function binarySearchIndex<T, V>(
  arr: T[],
  target: V,
  keyFn: (item: T) => V,
  compare: (a: V, b: V) => number = defaultCompare
): number {
  let left = 0;
  let right = arr.length - 1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const midValue = keyFn(arr[mid]);
    const comparison = compare(midValue, target);

    if (comparison === 0) {
      // Target found, return its index
      return mid;
    } else if (comparison < 0) {
      // Search in the right half
      left = mid + 1;
    } else {
      // Search in the left half
      right = mid - 1;
    }
  }

  // Target not found
  return -1;
}

export function binarySearch<T, V>(
  arr: T[],
  target: V,
  keyFn: (item: T) => V,
  compare: (a: V, b: V) => number = defaultCompare
): T | undefined {
  const index = binarySearchIndex(arr, target, keyFn, compare);
  return index !== -1 ? arr[index] : undefined;
}

export function autoScaleInfoProviderCreator(defaultPriceRange) {
  return ((baseImplementation) => {
    const base = baseImplementation();
    const priceRange = base?.priceRange ?? defaultPriceRange

    return {
      priceRange: {
        maxValue: Math.max(defaultPriceRange.maxValue, priceRange.maxValue),
        minValue: Math.min(defaultPriceRange.minValue, priceRange.minValue),
      }
    }
  }) satisfies AutoscaleInfoProvider
}